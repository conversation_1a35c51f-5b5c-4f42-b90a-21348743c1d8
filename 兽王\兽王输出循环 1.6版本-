#已修复的问题
#修复开场时集中值不足50且倒刺射击没有充能时，延迟狂野怒火的释放
#修复错过最佳的开场爆发时机,影响整体DPS输出
#单体环境：time<5 - 战斗开始5秒内立即释放狂野怒火
#多目标环境：time<5&active_enemies>=3 - 3个以上敌人时才开场爆发
#1. 饰品变量冲突 
#修复前：存在未使用的stronger_trinket_slot变量和重复的饰品变量定义
#修复后：统一使用trinket_1_stronger和trinket_2_stronger，在precombat阶段定义，trinkets阶段直接使用
#2. 重复变量定义 
#修复前：饰品变量在precombat和trinkets两个地方都有定义
#修复后：只在precombat阶段定义一次，避免重复
#3. 集中值检查不一致 
#修复前：多重射击使用focus>=40，其他技能使用focus>=30
#修复后：统一使用focus>=30标准，保持一致性

# 优化日期：2025-08-24 2:00

# 移除了过时的 boss& 判断条件使用1.1版本的.
# 恢复成1.1版本的饰品使用逻辑.  
# 参考1.3版本.微调爆发预判时间： 
# 将 cooldown.bestial_wrath.remains<=3 改为 <=2 
# 让爆发技能提前1秒准备 
# 优化开场爆发： 
# 强化战斗前5秒的爆发逻辑 
# 确保开场就能快速进入爆发状态
# 添加缺失的cycle_targets=1参数,(用于判断目标数)
# 优化日期：2025-08-24 1:41

# 移除过时的boss&判断条件，使用1.1版本逻辑
# 恢复1.1版本饰品使用逻辑
# 微调爆发预判时间：cooldown.bestial_wrath.remains<=3改为<=2
# 优化开场爆发：强化战斗前5秒的爆发逻辑
# 优化日期：2025-08-23

hunter="ddd兽王V1.6"
source=default
spec=beast_mastery
level=80
race=human
role=attack
position=ranged_back
professions=leatherworking=69/engineering=27
talents=C0PA11Mk8aZ38kAf+zso3nZ9IAMmxwCZDmRDNsBAAAAAgxMm5BGzMDmZGsMjZmxYmZmMjZMzMzgZGGGjZmhZbYYmhNAAAAAAYGA

# 消耗品配置
potion=tempered_potion_3
flask=flask_of_alchemical_chaos_3
food=the_sushi_special
augmentation=crystallized
temporary_enchant=main_hand:algari_mana_oil_3

# 战前准备 - 基于魔兽世界文件高级优化系统
actions.precombat=summon_pet
actions.precombat+=/snapshot_stats
# 饰品强度判断变量 - 使用1.1版本简化逻辑
actions.precombat+=/variable,name=trinket_1_stronger,value=!trinket.2.has_cooldown|trinket.1.has_use_buff&(!trinket.2.has_use_buff|trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time)
actions.precombat+=/variable,name=trinket_2_stronger,value=!variable.trinket_1_stronger

# 猎群领袖爆发期状态变量 - 融合1614点AOE的爆发窗口优化
actions.precombat+=/variable,name=pack_leader_burst,value=0
actions.precombat+=/variable,name=trinket_window,value=0
actions.precombat+=/variable,name=cotw_window,value=0
actions.precombat+=/variable,name=burst_incoming,value=0

# 兽王核心变量系统 - 微调爆发预判时间
actions.precombat+=/variable,name=bear_priority,value=buff.howl_of_the_pack_leader_bear.up|cooldown.pack_leader.remains<8
actions.precombat+=/variable,name=perfect_storm,value=buff.howl_of_the_pack_leader_bear.up&buff.bestial_wrath.up&debuff.barbed_shot.stack>=3
actions.precombat+=/variable,name=focus_threshold_high,value=75
actions.precombat+=/variable,name=focus_threshold_low,value=40
actions.precombat+=/variable,name=burst_window_active,value=buff.bestial_wrath.up|cooldown.bestial_wrath.remains<2

# 战前倒刺射击设置选项 - 参考默认参数文件
actions.precombat+=/barbed_shot,if=settings.barbed_shot_opener
# 狩猎大师层数卡控 - 基于兽王循环手法理论，插钥匙前卡4-5层
actions.precombat+=/barbed_shot,if=!buff.hunter_master.up|buff.hunter_master.stack<5
actions.precombat+=/kill_command,if=buff.hunter_master.stack>=4&buff.hunter_master.stack<5

# 主循环 - 基于魔兽世界文件高级优化系统
actions=auto_shot
# 反制技能优先级 - 融合1614点AOE备份的实用功能
actions+=/counter_shot
actions+=/tranquilizing_shot
# 修复后的治疗宠物逻辑
actions+=/mend_pet,if=pet.health_pct<pet_healing
# 猎人印记管理
actions+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20

# 动态变量实时更新 - 微调爆发预判时间
actions+=/variable,name=perfect_storm,value=buff.howl_of_the_pack_leader_bear.up&buff.bestial_wrath.up&debuff.barbed_shot.stack>=3
actions+=/variable,name=burst_window_active,value=buff.bestial_wrath.up|cooldown.bestial_wrath.remains<2
actions+=/variable,name=focus_starved,value=focus<variable.focus_threshold_low&focus.regen<15
# 猎群领袖爆发期管理 - 微调爆发预判时间，让爆发技能提前1秒准备
actions+=/variable,name=pack_leader_burst,value=buff.call_of_the_wild.up|buff.bestial_wrath.up|buff.bloodshed.up|buff.lead_from_the_front.up|buff.hunters_prey.up|cooldown.call_of_the_wild.remains<2|cooldown.bestial_wrath.remains<2|cooldown.bloodshed.remains<2
actions+=/variable,name=burst_incoming,value=cooldown.call_of_the_wild.remains<4|cooldown.bestial_wrath.remains<4|cooldown.bloodshed.remains<4

actions+=/call_action_list,name=cds
actions+=/call_action_list,name=trinkets
# 参考官方推导的目标数量判断逻辑
actions+=/call_action_list,name=drst,if=talent.black_arrow&(active_enemies<2|!talent.beast_cleave&active_enemies<3)
actions+=/call_action_list,name=drcleave,if=talent.black_arrow&(active_enemies>2|talent.beast_cleave&active_enemies>1)
actions+=/call_action_list,name=st,if=!talent.black_arrow&(active_enemies<2|!talent.beast_cleave&active_enemies<3)
actions+=/call_action_list,name=cleave,if=!talent.black_arrow&(active_enemies>2|talent.beast_cleave&active_enemies>1)

# CD技能管理 - 使用1.1版本逻辑
actions.cds=invoke_external_buff,name=power_infusion,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&(buff.bestial_wrath.up|cooldown.bestial_wrath.remains<30)|fight_remains<16
# 种族技能 - 恢复1.1版本判断条件
actions.cds+=/berserking,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|fight_remains<13
actions.cds+=/blood_fury,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|fight_remains<16
actions.cds+=/ancestral_call,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|fight_remains<16
actions.cds+=/fireblood,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|fight_remains<9
# 药水使用 - 恢复1.1版本判断条件
actions.cds+=/potion,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|fight_remains<31

# 单体循环（无黑蚀箭天赋）- 基于魔兽世界文件高级优化
# 完美风暴状态 - 最高优先级爆发
actions.st=kill_command,if=variable.perfect_storm&action.kill_command.charges>=1
actions.st+=/barbed_shot,cycle_targets=1,if=variable.perfect_storm&action.barbed_shot.charges>=1&debuff.barbed_shot.stack<3

# 套装效果优化的狂野怒火
actions.st+=/bestial_wrath,if=buff.howl_of_the_pack_leader_cooldown.remains-buff.lead_from_the_front.duration<buff.lead_from_the_front.duration%gcd*0.5|!set_bonus.tww3_4pc
# 优化开场爆发：参考单体文件，简化开场条件确保E开头
actions.st+=/bestial_wrath,if=cooldown.bestial_wrath.ready&time<5

# 倒刺射击绝对优先 - 微调爆发预判时间，添加cycle_targets参数
actions.st+=/barbed_shot,cycle_targets=1,if=action.barbed_shot.charges>=2
actions.st+=/barbed_shot,cycle_targets=1,if=action.barbed_shot.charges>=1&cooldown.bestial_wrath.remains<=2
actions.st+=/barbed_shot,cycle_targets=1,if=full_recharge_time<gcd

actions.st+=/call_of_the_wild
actions.st+=/bloodshed

# 熊在场时杀戮命令优先级提升 - 基于兽王循环手法理论
actions.st+=/kill_command,if=buff.howl_of_the_pack_leader_bear.up&charges>=1
actions.st+=/kill_command,if=charges_fractional>=cooldown.barbed_shot.charges_fractional&!(buff.lead_from_the_front.remains>gcd&buff.lead_from_the_front.remains<gcd*2&!howl_summon.ready&action.barbed_shot.full_recharge_time>gcd)

# 倒刺射击3层叠加机制 - 确保倒刺不溢出，添加cycle_targets参数
actions.st+=/barbed_shot,cycle_targets=1,if=debuff.barbed_shot.stack<3|action.barbed_shot.charges>=2
actions.st+=/barbed_shot,cycle_targets=1

# 红人内眼镜蛇射击重置杀戮 - 基于兽王循环手法理论
actions.st+=/cobra_shot,if=buff.bestial_wrath.up&action.kill_command.charges<1&focus>35
actions.st+=/cobra_shot,if=variable.focus_starved&action.kill_command.charges<1&action.barbed_shot.charges<1
actions.st+=/cobra_shot

# 多目标循环（无黑蚀箭天赋）- 融合1614点AOE的激进优化策略
# 完美风暴AOE状态 - 最高优先级
actions.cleave=kill_command,if=variable.perfect_storm&action.kill_command.charges>=1&active_enemies<=6
actions.cleave+=/barbed_shot,cycle_targets=1,if=variable.perfect_storm&action.barbed_shot.charges>=1&debuff.barbed_shot.stack<2

# 套装效果优化的狂野怒火 - 融合1614点AOE的时机优化
actions.cleave+=/bestial_wrath,if=buff.howl_of_the_pack_leader_cooldown.remains-buff.lead_from_the_front.duration<buff.lead_from_the_front.duration%gcd*0.5|!set_bonus.tww3_4pc
# AOE开场爆发：参考单体文件，确保多目标环境下的E开头
actions.cleave+=/bestial_wrath,if=cooldown.bestial_wrath.ready&time<5&active_enemies>=3

# 倒刺射击 - 融合1614点AOE的优化充能管理，配合野性呼唤和爆发期预判，添加cycle_targets参数
actions.cleave+=/barbed_shot,cycle_targets=1,if=full_recharge_time<gcd|charges_fractional>=cooldown.kill_command.charges_fractional|talent.call_of_the_wild&cooldown.call_of_the_wild.ready|howl_summon.ready&full_recharge_time<8|variable.burst_incoming&charges>=1.5

# 血腥撕裂 - 猎群领袖的重要爆发技能
actions.cleave+=/bloodshed,if=talent.bloodshed

# 多重射击 - 维护宠物兽性切割BUFF，统一集中值检查标准
actions.cleave+=/multishot,if=pet.main.buff.beast_cleave.down&(!talent.bloody_frenzy|cooldown.call_of_the_wild.remains)&focus>=30

# 野性呼唤 - 猎群领袖的核心爆发技能
actions.cleave+=/call_of_the_wild,if=talent.call_of_the_wild

# 爆炸射击 - 配合雷鸣蹄声天赋
actions.cleave+=/explosive_shot,if=talent.thundering_hooves&talent.explosive_shot

# 杀戮指令 - 核心技能，添加集中值检查（融合1614点AOE优化）
actions.cleave+=/kill_command,if=focus>=30

# 眼镜蛇射击 - 最后的填充技能，只在集中值即将溢出或特殊情况下使用（融合1614点AOE优化）
actions.cleave+=/cobra_shot,if=focus.time_to_max<gcd*2|buff.hogstrider.stack>3|!talent.multishot

# 单体循环（有黑蚀箭天赋）- 融合1614点AOE的优化爆发期配合
actions.drst=kill_shot
# 狂野怒火 - 避免与野性呼唤冲突（融合1614点AOE优化）
actions.drst+=/bestial_wrath,if=cooldown.call_of_the_wild.remains>20|!talent.call_of_the_wild

# 倒刺射击 - 快速充能时优先（融合1614点AOE优化），添加cycle_targets参数
actions.drst+=/barbed_shot,cycle_targets=1,if=full_recharge_time<gcd
# 血腥撕裂 - 猎群领袖重要爆发技能
actions.drst+=/bloodshed,if=talent.bloodshed
# 野性呼唤 - 猎群领袖核心技能
actions.drst+=/call_of_the_wild,if=talent.call_of_the_wild
# 杀戮指令 - 核心技能，添加集中值检查（融合1614点AOE优化）
actions.drst+=/kill_command,if=focus>=30
# 倒刺射击 - 维持DOT，添加cycle_targets参数
actions.drst+=/barbed_shot,cycle_targets=1
# 眼镜蛇射击 - 最后的填充技能，只在集中值即将溢出时使用（融合1614点AOE优化）
actions.drst+=/cobra_shot,if=focus.time_to_max<gcd*2

# 多目标循环（有黑蚀箭天赋）- 融合1614点AOE的优化爆发期配合
actions.drcleave=kill_shot
# 狂野怒火 - 避免与野性呼唤冲突（融合1614点AOE优化）
actions.drcleave+=/bestial_wrath,if=cooldown.call_of_the_wild.remains>20|!talent.call_of_the_wild

# 倒刺射击 - 快速充能时优先（融合1614点AOE优化），添加cycle_targets参数
actions.drcleave+=/barbed_shot,cycle_targets=1,if=full_recharge_time<gcd
# 血腥撕裂 - 猎群领袖重要爆发技能
actions.drcleave+=/bloodshed,if=talent.bloodshed
# 多重射击 - 维护宠物BUFF（融合1614点AOE优化）
actions.drcleave+=/multishot,if=pet.main.buff.beast_cleave.down&(!talent.bloody_frenzy|cooldown.call_of_the_wild.remains)
# 野性呼唤 - 猎群领袖核心技能
actions.drcleave+=/call_of_the_wild,if=talent.call_of_the_wild
# 爆炸射击 - 配合雷鸣蹄声
actions.drcleave+=/explosive_shot,if=talent.thundering_hooves&talent.explosive_shot
# 倒刺射击 - 充能管理，添加cycle_targets参数
actions.drcleave+=/barbed_shot,cycle_targets=1,if=charges_fractional>=cooldown.kill_command.charges_fractional
# 杀戮指令 - 核心技能，添加集中值检查（融合1614点AOE优化）
actions.drcleave+=/kill_command,if=focus>=30
# 眼镜蛇射击 - 最后的填充技能，只在集中值即将溢出时使用（融合1614点AOE优化）
actions.drcleave+=/cobra_shot,if=focus.time_to_max<gcd*2
# 爆炸射击 - 收尾技能
actions.drcleave+=/explosive_shot,if=talent.explosive_shot

# 饰品管理 - 恢复1.1版本简化逻辑

# 1.1版本饰品使用逻辑 - 简化判断条件
actions.trinkets=use_item,use_off_gcd=1,slot=trinket1,if=trinket.1.has_use_buff&(buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|fight_remains<20)
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket2,if=trinket.2.has_use_buff&(buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|fight_remains<20)
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket1,if=!trinket.1.has_use_buff&(variable.trinket_1_stronger|trinket.2.cooldown.remains)
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket2,if=!trinket.2.has_use_buff&(variable.trinket_2_stronger|trinket.1.cooldown.remains)

# 装备配置
head=midnight_heralds_cowl,id=237646,bonus_id=6652/12921/10390/12231/12353/1514/10255
neck=amulet_of_earthen_craftsmanship,id=215136,bonus_id=10421/9633/8902/10879/10396/9627/12040/11941/8790/12043/12373,gem_id=213455/213455,crafted_stats=36/32
shoulders=tireless_collectors_hunted_heads,id=229269,bonus_id=6652/12376/10390/12179/11962/1533
back=reshii_wraps,id=235499,bonus_id=9877/12399,gem_id=238044,enchant_id=7409
chest=tireless_collectors_battlegear,id=229274,bonus_id=10356/11958/6652/12178/12376/1533/10255,enchant_id=7364
shirt=lucky_shirt,id=138385
tabard=renowned_guild_tabard,id=69210
wrists=glyphetched_vambraces,id=219342,bonus_id=10421/9633/8902/9627/12043/11303/8960/8790/12040/12056/12373,gem_id=213455,enchant_id=7397,crafted_stats=32/40
hands=gloves_of_whispering_winds,id=156317,bonus_id=7756/12239/10383/12295/11402/10255
waist=durable_information_securing_container,id=245965,bonus_id=12533/1489,gem_id=213455,titan_disc_id=1236275
legs=midnight_heralds_petticoat,id=237645,bonus_id=12232/6652/12676/12289/1501/10255,enchant_id=7601
feet=boots_of_unsettled_prey,id=156467,bonus_id=12350/8902/7756/12239/10383/11399/10255,enchant_id=7418
finger1=signet_of_the_false_accuser,id=178824,bonus_id=12352/10390/6652/10395/10392/10383/10010/10255
finger2=ring_of_earthen_craftsmanship,id=215135,bonus_id=10421/9633/8902/10879/10396/9627/12040/10518/8960/8790/12043/12373,gem_id=213455/213455,enchant_id=7334,crafted_stats=36/32
trinket1=house_of_cards,id=230027,bonus_id=6652/10356/12376/1533/10255
trinket2=improvised_seaforium_pacemaker,id=232541,bonus_id=10390/6652/10383/12376/1520/10255
main_hand=tiny_pal,id=228893,bonus_id=41/10356/12060/12376/1533/10255,enchant_id=7439

# 装备统计
# gear_ilvl=691.00
# gear_agility=63787
# gear_stamina=470376
# gear_crit_rating=17648
# gear_haste_rating=17873
# gear_mastery_rating=8510
# gear_versatility_rating=1232
# gear_leech_rating=2064
# gear_speed_rating=1250
# gear_armor=51624
# set_bonus=thewarwithin_season_2_2pc=1
# set_bonus=name=thewarwithin_season_3,pc=2,hero_tree=pack_leader,enable=1