## https://github.com/simulationcraft/simc/blob/thewarwithin/ActionPriorityLists/default/hunter_survival.simc
## SimulationCraft Commit Sync: 7badbb5
## Date: 2025-08-04

actions.precombat+=/summon_pet
actions.precombat+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20
actions.precombat+=/variable,name=stronger_trinket_slot,op=setif,value=1,value_else=2,condition=!trinket.2.is.house_of_cards&(trinket.1.is.house_of_cards|!trinket.2.has_cooldown|trinket.1.has_use_buff&(!trinket.2.has_use_buff|trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration)|!trinket.1.has_use_buff&(!trinket.2.has_use_buff&(trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration)))

actions+=/muzzle
actions+=/tranquilizing_shot
actions+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20
actions+=/mend_pet,if=pet.health_pct<pet_healing
actions+=/call_action_list,name=cds
actions+=/call_action_list,name=plst,strict=1,if=active_enemies<3&talent.howl_of_the_pack_leader
actions+=/call_action_list,name=plcleave,strict=1,if=active_enemies>2&talent.howl_of_the_pack_leader
actions+=/call_action_list,name=sentst,strict=1,if=active_enemies<3&!talent.howl_of_the_pack_leader
actions+=/call_action_list,name=sentcleave,strict=1,if=active_enemies>2&!talent.howl_of_the_pack_leader
# Use Kill Command if there's nothing else to press
actions+=/kill_command,cycle_targets=1
actions+=/arcane_torrent
actions+=/bag_of_tricks
actions+=/lights_judgment

# COOLDOWNS ACTIONLIST
actions.cds+=/harpoon,if=settings.use_harpoon&prev.kill_command
actions.cds+=/blood_fury,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
## actions.cds+=/invoke_external_buff,name=power_infusion,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/ancestral_call,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/fireblood,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/berserking,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault|time_to_die<13
actions.cds+=/potion,if=boss&fight_remains<25|buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/use_item,name=imperfect_ascendancy_serum,use_off_gcd=1,if=gcd.remains>gcd.max-0.1
actions.cds+=/use_items,if=cooldown.coordinated_assault.remains|cooldown.spearhead.remains
actions.cds+=/aspect_of_the_eagle,if=target.distance>=6
actions.cds+=/use_item,name=spellstrike_warplance


#PACK LEADER |  AOE ACTIONLIST
actions.plcleave+=/spearhead,if=cooldown.coordinated_assault.remains
actions.plcleave+=/kill_command,if=buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1
actions.plcleave+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/wildfire_bomb,if=cooldown.wildfire_bomb.charges_fractional>1.7
actions.plcleave+=/explosive_shot,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/raptor_bite,cycle_targets=1,if=buff.strike_it_rich.up&buff.strike_it_rich.remains<gcd|buff.hogstrider.remains&boar_charge.remains>0|buff.hogstrider.remains<gcd&buff.hogstrider.up|buff.hogstrider.remains&buff.strike_it_rich.remains|raid_event.adds.exists&raid_event.adds.remains<4
actions.plcleave+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/kill_command,if=(buff.howl_of_the_pack_leader_wyvern.remains|buff.howl_of_the_pack_leader_boar.remains|buff.howl_of_the_pack_leader_bear.remains)
actions.plcleave+=/flanking_strike,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/butchery,if=cooldown.wildfire_bomb.charges_fractional<1.5
actions.plcleave+=/coordinated_assault,if=buff.howl_of_the_pack_leader_cooldown.remains-buff.lead_from_the_front.duration<buff.lead_from_the_front.duration%gcd*0.6
actions.plcleave+=/kill_command,if=focus+cast_regen<focus.max
actions.plcleave+=/explosive_shot
actions.plcleave+=/kill_shot,if=buff.deathblow.remains&talent.sic_em
## actions.plcleave+=/raptor_bite,cycle_targets=1,if=!talent.contagious_reagents
actions.plcleave+=/raptor_bite,cycle_targets=1

# PACK LEADER | SINGLE TARGET ACTIONLIST.
actions.plst+=/kill_command,if=(buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1)|(buff.howl_of_the_pack_leader_wyvern.remains|buff.howl_of_the_pack_leader_boar.remains|buff.howl_of_the_pack_leader_bear.remains)
actions.plst+=/spearhead,if=cooldown.coordinated_assault.remains
actions.plst+=/flanking_strike,if=buff.tip_of_the_spear.stack>0&(cooldown.spearhead.remains>5|!talent.spearhead&cooldown.coordinated_assault.remains>5)
actions.plst+=/raptor_bite,cycle_targets=1,if=!dot.serpent_sting.ticking&target.time_to_die>12&(!talent.contagious_reagents|active_dot.serpent_sting=0)
actions.plst+=/raptor_bite,cycle_targets=1,if=talent.contagious_reagents&active_dot.serpent_sting<active_enemies&dot.serpent_sting.remains
actions.plst+=/kill_command,if=buff.strike_it_rich.remains&buff.tip_of_the_spear.stack<1
actions.plst+=/raptor_bite,if=buff.strike_it_rich.remains&buff.tip_of_the_spear.stack>0
actions.plst+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0&(!raid_event.adds.exists|raid_event.adds.exists&raid_event.adds.in>40)
actions.plst+=/coordinated_assault,if=buff.howl_of_the_pack_leader_cooldown.remains-buff.lead_from_the_front.duration<buff.lead_from_the_front.duration%gcd*0.6|time_to_die<20|!talent.spearhead
actions.plst+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.plst+=/raptor_bite,cycle_targets=1,if=buff.howl_of_the_pack_leader_cooldown.up&buff.howl_of_the_pack_leader_cooldown.remains<2*gcd
actions.plst+=/kill_command,if=focus+cast_regen<focus.max&(!buff.relentless_primal_ferocity.up|(buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<2|focus<30))
actions.plst+=/explosive_shot,if=active_enemies>1
actions.plst+=/kill_shot,if=talent.cull_the_herd
## actions.plst+=/raptor_bite,cycle_targets=1,if=!talent.contagious_reagents
actions.plst+=/raptor_bite,cycle_targets=1
actions.plst+=/kill_shot
actions.plst+=/explosive_shot

# SENTINEL | DEFAULT AOE ACTIONLIST
actions.sentcleave+=/wildfire_bomb,if=!buff.lunar_storm_cooldown.remains
actions.sentcleave+=/kill_command,if=buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1
actions.sentcleave+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0|cooldown.wildfire_bomb.charges_fractional>1.9|(talent.bombardier&cooldown.coordinated_assault.remains<2*gcd)|talent.butchery&cooldown.butchery.remains<gcd
actions.sentcleave+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0
actions.sentcleave+=/raptor_bite,cycle_targets=1,if=buff.strike_it_rich.up&buff.strike_it_rich.remains<gcd
actions.sentcleave+=/butchery
actions.sentcleave+=/explosive_shot,if=buff.tip_of_the_spear.stack>0
actions.sentcleave+=/coordinated_assault
actions.sentcleave+=/flanking_strike,if=(buff.tip_of_the_spear.stack=2|buff.tip_of_the_spear.stack=1)
actions.sentcleave+=/kill_command,if=focus+cast_regen<focus.max
actions.sentcleave+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.sentcleave+=/explosive_shot
actions.sentcleave+=/kill_shot,if=buff.deathblow.remains&talent.sic_em
## actions.sentcleave+=/raptor_bite,cycle_targets=1,if=!talent.contagious_reagents
actions.sentcleave+=/raptor_bite,cycle_targets=1

# SENTINEL | DEFAULT SINGLE TARGET ACTIONLIST.
actions.sentst+=/wildfire_bomb,if=!buff.lunar_storm_cooldown.remains&buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,if=(buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1)
actions.sentst+=/spearhead,if=cooldown.coordinated_assault.remains
actions.sentst+=/flanking_strike,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,if=buff.strike_it_rich.remains&buff.tip_of_the_spear.stack<1
actions.sentst+=/mongoose_bite,if=buff.strike_it_rich.remains&buff.coordinated_assault.up
actions.sentst+=/wildfire_bomb,if=cooldown.wildfire_bomb.charges_fractional>1.7
actions.sentst+=/butchery
actions.sentst+=/coordinated_assault,if=!talent.bombardier|talent.bombardier&cooldown.wildfire_bomb.charges_fractional<2
actions.sentst+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,if=buff.tip_of_the_spear.stack<1&cooldown.flanking_strike.remains<gcd
actions.sentst+=/kill_command,if=focus+cast_regen<focus.max&(!buff.relentless_primal_ferocity.up|(buff.relentless_primal_ferocity.up&(buff.tip_of_the_spear.stack<1|focus<30)))
actions.sentst+=/mongoose_bite,if=buff.mongoose_fury.remains<gcd&buff.mongoose_fury.stack>0
actions.sentst+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/explosive_shot
actions.sentst+=/mongoose_bite,if=buff.mongoose_fury.remains
actions.sentst+=/kill_shot
## actions.sentst+=/raptor_bite,cycle_targets=1,if=!talent.contagious_reagents
actions.sentst+=/raptor_bite,cycle_targets=1

# SENTINEL | DEFAULT SINGLE TARGET ACTIONLIST.
actions.sentst+=/wildfire_bomb,if=!buff.lunar_storm_cooldown.remains
actions.sentst+=/kill_command,cycle_targets=1,if=(buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1)
actions.sentst+=/spearhead,if=cooldown.coordinated_assault.remains
actions.sentst+=/flanking_strike,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,if=buff.strike_it_rich.remains&buff.tip_of_the_spear.stack<1
actions.sentst+=/mongoose_bite,if=buff.strike_it_rich.remains&buff.coordinated_assault.up
actions.sentst+=/wildfire_bomb,if=(buff.lunar_storm_cooldown.remains>full_recharge_time-gcd)&(buff.tip_of_the_spear.stack>0&cooldown.wildfire_bomb.charges_fractional>1.7|cooldown.wildfire_bomb.charges_fractional>1.9)|(talent.bombardier&cooldown.coordinated_assault.remains<2*gcd)
actions.sentst+=/butchery
actions.sentst+=/coordinated_assault,if=!talent.bombardier|talent.bombardier&cooldown.wildfire_bomb.charges_fractional<1
actions.sentst+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,cycle_targets=1,if=buff.tip_of_the_spear.stack<1&cooldown.flanking_strike.remains<gcd
actions.sentst+=/kill_command,cycle_targets=1,if=focus+cast_regen<focus.max&(!buff.relentless_primal_ferocity.up|(buff.relentless_primal_ferocity.up&(buff.tip_of_the_spear.stack<2|focus<30)))
actions.sentst+=/mongoose_bite,if=buff.mongoose_fury.remains<gcd&buff.mongoose_fury.stack>0
actions.sentst+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0&buff.lunar_storm_cooldown.remains>full_recharge_time&(!raid_event.adds.exists|raid_event.adds.exists&raid_event.adds.in>15)
actions.sentst+=/mongoose_bite,if=buff.mongoose_fury.remains
actions.sentst+=/explosive_shot
actions.sentst+=/kill_shot
actions.sentst+=/raptor_bite,cycle_targets=1,if=!talent.contagious_reagents
actions.sentst+=/raptor_bite,cycle_targets=1