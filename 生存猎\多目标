
SimulationCraft 1120-01
for World of Warcraft 11.2.0.62748 Live (hotfix 2025-08-26/62748, git build 392428007d)
Timestamp: 2025-08-28 06:49:12+0000Iterations: 4812Fight Length: 240 - 360Fight Style: Patchwerk
Current simulator hotfixes
哈士奇的远征 : 11,498,198 dps, 3,392,630 dps to main target
Race: HumanClass: HunterSpec: SurvivalLevel: 80Role: AttackPosition: backProfile Source: default
Results, Spec and Gear

DPS	DPS(e)	DPS Error	DPS Range	DPR
11,498,197.9	11,498,197.9	5,664.1 / 0.049%	772,883.2 / 6.7%	1,005,856.1
Resource	Out	In	Waiting	APM	Active
Focus	11.2	11.1	0.04%	53.9	100.0%
Talent	C8PA11Mk8aZ38kAf+zso3nZ9IMGYglxoxyAysFsNzYZmZmZmhxMzMjxYMzYWAAAAAAAaGzYGzMDzwMMGmZYMMLLzgNAAAAAYAA
Set Bonus	Midnight Herald's Pledge (TWW3) Hunter Sentinel 11.2 Class Set 2pc (2pc) Hunter Sentinel 11.2 Class Set 4pc (4pc)
Professions	leatherworking: 69 engineering: 27
Charts
 
 
 
 
Abilities
哈士奇的远征	11,498,198	
auto_attack_mh	97,977	0.9%	204.3	1.76s	143,655	86,173	Direct	204.3	96,605	193,871	143,651	48.4%	
Butchery	392,457 (961,067)	3.4% (8.4%)	25.2	12.05s	11,427,695	10,084,693	Direct	125.9 (1,506.3)	624,107	1,265,925	933,670	48.2% (48.2%)	
    (butchery_) Merciless Blow	568,610	4.9%	0.0	0.00s	0	0	Periodic	1,380.4	82,119	166,597	123,292	48.7%	66.2%
Coordinated Assault	0 (38,410)	0.0% (0.3%)	6.9	46.34s	1,667,054	1,424,491	
    Coordinated Assault (_player)	14,381	0.1%	0.0	0.00s	0	0	Direct	6.9	429,437	857,941	623,964	45.4%	
    Coordinated Assault	24,029 / 24,029	0.2%	6.9	46.34s	1,042,983	0	Direct	6.9	716,417	1,432,246	1,042,868	45.6%	
Ethereal Reaping	336,134	2.9%	14.6	19.50s	6,905,849	0	Direct	72.9	932,103	1,861,358	1,381,179	48.3%	
Explosive Shot (_damage)	1,276,451	11.1%	0.0	0.00s	0	0	Direct	198.3	1,293,773	2,623,642	1,929,181	47.8%	
Fury of the Eagle	0 (775,581)	0.0% (6.7%)	6.6	48.08s	35,300,918	13,747,292	
    Fury of the Eagle (_damage)	775,581	6.7%	45.9	6.01s	5,061,068	0	Direct	229.3	635,713	1,274,144	1,012,249	59.0%	
Kill Command	0 (19,632)	0.0% (0.2%)	64.4	4.50s	91,355	79,844	
    Arcane Shot (_quick_shot)	19,632	0.2%	0.0	0.00s	0	0	Direct	14.9	263,459	535,347	395,350	48.5%	
Lunar Storm (_initial)	59,081	0.5%	0.0	0.00s	0	0	Direct	51.3	233,273	469,165	344,771	47.3%	
Lunar Storm (_periodic)	363,875	3.2%	0.0	0.00s	0	0	Direct	301.5	242,346	490,028	361,679	48.2%	
Raptor Strike	169,043	1.5%	43.1	6.70s	1,176,409	1,024,619	Direct	43.1	785,490	1,597,249	1,176,332	48.2%	
Sentinel	767,914	6.7%	0.0	0.00s	0	0	Periodic	586.8	263,112	531,666	392,173	48.1%	0.0%
Serpent Sting (_vipers_venom)	488,854	4.3%	0.0	0.00s	0	0	Direct	201.5	88,598	179,093	131,897	47.8%	
Periodic	577.3	140,067	282,276	207,862	47.7%	88.1%
Symphonic Arsenal	608,164	5.3%	0.0	0.00s	0	0	Direct	540.5	225,374	456,218	336,890	48.3%	
Wildfire Bomb	0 (5,334,832)	0.0% (46.4%)	85.8	3.54s	18,626,037	16,409,860	
    Wildfire Bomb (_damage)	3,818,920	33.2%	0.0	0.00s	0	0	Direct	428.7	1,781,034	3,609,150	2,667,993	48.5%	
    Wildfire Bomb (_dot)	1,515,912	13.2%	0.0	0.00s	0	0	Periodic	1,478.5	203,651	418,715	307,068	48.1%	98.7%
pet - duck	225213 / 225213	
Claw	18,621	0.2%	94.8	3.18s	58,810	58,546	Direct	94.8	39,084	80,077	58,806	48.1%	
Kill Command	71,663	0.6%	64.4	4.50s	333,652	0	Direct	64.4	222,589	450,683	333,634	48.7%	
melee	110,899	1.0%	475.5	0.63s	69,879	110,974	Direct	475.5	46,864	94,629	69,880	48.2%	
哈士奇的远征
Crystallized Augment Rune	1.0	0.00s
reshii_wraps	14.6	19.50s
Explosive Shot	22.6	13.25s
Flask of Alchemical Chaos	1.0	0.00s
The Sushi Special	1.0	0.00s
Harpoon	9.3	32.53s
Tempered Potion	1.5	306.57s
signet_of_the_priory	2.8	136.94s
Call Pet 1 (summon_pet)	1.0	0.00s
Buffs
Bloodlust	1.0	0.0	0.0s	0.0s	40.0s	13.53%	0.00%	0.0 (0.0)	1.0
Bloodseeker	1.0	0.0	0.0s	0.0s	288.8s	96.34%	0.00%	0.0 (0.0)	0.0
Bolstering Light (Crit)	2.8	0.0	137.3s	137.3s	18.8s	17.51%	0.00%	0.0 (0.0)	2.5
Bolstering Light (Vers)	0.0	0.0	0.0s	0.0s	16.2s	0.01%	0.00%	0.0 (0.0)	0.0
Bombardier	6.4	0.0	46.3s	46.3s	2.6s	5.61%	23.19%	0.0 (0.0)	0.0
Boon of Elune (_2pc)	9.9	0.0	30.7s	30.7s	6.1s	19.98%	21.56%	0.0 (0.0)	0.0
Boon of Elune (_4pc)	10.3	0.0	30.7s	30.7s	11.8s	40.34%	38.07%	0.0 (0.0)	0.0
Coordinated Assault	6.9	0.0	46.3s	46.3s	19.4s	44.63%	0.00%	0.0 (0.0)	6.4
Deathblow	5.2	1.3	50.5s	38.8s	13.3s	23.00%	0.00%	1.3 (1.3)	4.9
Endurance Training	1.0	0.0	0.0s	0.0s	299.6s	100.00%	0.00%	0.0 (0.0)	0.0
Explosive Adrenaline	5.4	0.0	60.7s	60.7s	28.6s	51.98%	0.00%	0.0 (0.0)	4.9
Flask of Alchemical Chaos (Crit)	2.1	0.6	111.6s	76.6s	35.1s	25.01%	0.00%	2.9 (2.9)	0.0
Flask of Alchemical Chaos (Haste)	2.1	0.6	111.7s	76.5s	35.2s	24.84%	0.00%	2.9 (2.9)	0.0
Flask of Alchemical Chaos (Mastery)	2.1	0.6	111.9s	76.6s	35.1s	24.86%	0.00%	2.9 (2.9)	0.0
Flask of Alchemical Chaos (Vers)	2.2	0.6	111.3s	77.0s	35.1s	25.29%	0.00%	2.9 (2.9)	0.0
Lunar Storm (_cooldown)	10.3	0.0	30.7s	30.7s	28.6s	97.92%	0.00%	0.0 (0.0)	9.3
Lunar Storm (_ready)	10.3	0.0	30.6s	30.7s	0.6s	2.08%	11.97%	0.0 (0.0)	0.0
Maybe Stop Blowing Up	5.4	0.0	60.7s	60.7s	54.5s	98.96%	0.00%	0.0 (0.0)	4.5
Ruthless Marauder	6.6	0.0	48.4s	48.4s	9.8s	21.43%	0.00%	0.0 (0.0)	6.3
Static Charge	5.0	0.0	57.6s	33.3s	27.2s	45.16%	0.00%	0.0 (0.0)	2.1
Sulfur-Lined Pockets (sulfur_lined_pockets)	4.7	0.0	59.7s	59.7s	14.7s	23.08%	22.95%	0.0 (0.0)	0.0
Sulfur-Lined Pockets (sulfur_lined_pockets_building)	5.2	9.7	58.1s	19.0s	28.0s	48.58%	0.00%	0.0 (0.0)	0.0
Tempered Potion	1.5	0.0	307.0s	307.0s	26.8s	12.81%	0.00%	0.0 (0.0)	1.0
Tip of the Spear	66.7	50.4	4.5s	2.5s	2.0s	45.29%	52.27%	30.0 (30.0)	0.0
Tip of the Spear (_explosive)	8.4	0.2	35.2s	34.4s	2.9s	8.17%	0.00%	0.0 (0.0)	0.0
Tip of the Spear (_fote)	6.6	0.0	48.3s	48.3s	2.3s	5.09%	0.00%	0.0 (0.0)	0.0
duck - duck: Bloodseeker	1.0	0.0	0.0s	0.0s	288.8s	96.34%	0.00%	0.0 (0.0)	0.0
Constant Buffs
Arcane Intellect
Battle Shout
Crystallization
Flask of Alchemical Chaos
Mark of the Wild
Power Word: Fortitude
Skyfury
The Sushi Special
Procs, Uptimes & Benefits
Deathblow	6.5	0.0	18.0	38.8s	0.9s	269.3s
Extrapolated Shots Stacks	40.3	19.0	68.0	8.5s	0.0s	89.5s
Kill Command Reset	16.9	4.0	32.0	16.7s	0.8s	179.7s
Overwatch Implosion	5.2	0.0	16.0	17.0s	0.0s	68.1s
Release and Reload Stacks	85.6	45.0	130.0	4.1s	0.0s	45.8s
Sentinel Implosions	21.2	11.0	34.0	14.2s	0.0s	114.7s
Sentinel Stacks	529.9	359.0	694.0	1.0s	0.0s	12.3s
Skyfury (Main Hand)	34.1	15.0	63.0	8.6s	1.2s	83.7s
delayed_aa_channel	8.8	5.0	14.0	34.5s	1.6s	88.5s
Focus Cap	1.72%	0.00%	5.75%	0.7s	0.0s	4.2s
duck - wild_hunt	7.54%	5.22%	11.84%
Cooldown waste details
Seconds per Execute	Seconds per Iteration
Ability	Average	Minimum	Maximum	Average	Minimum	Maximum
Harpoon	2.625	0.001	18.544	21.277	3.633	55.121
Kill Command	2.807	0.001	19.465	80.900	25.945	157.046
Fury of the Eagle	3.670	0.001	44.018	18.205	1.316	72.681
Wildfire Bomb	0.587	0.001	2.807	7.618	1.949	15.782
Explosive Shot	2.631	0.001	15.867	58.662	21.031	116.371
Butchery	0.716	0.001	10.863	16.409	2.828	40.823
Coordinated Assault	1.810	0.001	9.739	7.949	0.000	34.805
Resources
哈士奇的远征
Focus Regen	Focus	1,805.89	1,932.13	58.08%	1.07	40.82	2.07%
Invigorating Pulse	Focus	88.13	429.48	12.91%	4.87	11.17	2.54%
Kill Command	Focus	64.36	965.33	29.02%	15.00	0.00	0.00%
pet - duck
Focus Regen	Focus	734.57	2,465.04	100.00%	3.36	0.00	0.00%
Change	Start	Gain/s	Loss/s	Overflow	End (Avg)	Min	Max
Focus	100.0	11.10	11.20	52.0	70.0	1.0	100.0
哈士奇的远征
Butchery	Focus	25.35	760.44	22.50%	30.00	30.20	378,382.90
Explosive Shot	Focus	22.74	454.79	13.46%	20.00	20.13	0.00
Raptor Strike	Focus	43.35	1,300.62	38.49%	30.00	30.20	38,954.02
Wildfire Bomb	Focus	86.36	863.56	25.55%	10.00	10.07	1,850,221.90
pet - duck
Claw	Focus	94.79	2,546.04	100.00%	26.86	26.86	2,189.47
 
 
Statistics & Data Analysis
Action Priority List
Stats
Gear
Talents
Profile
hunter="哈士奇的远征"
source=default
spec=survival
level=80
race=human
role=attack
position=back
professions=leatherworking=69/engineering=27
talents=C8PA11Mk8aZ38kAf+zso3nZ9IMGYglxoxyAysFsNzYZmZmZmhxMzMjxYMzYWAAAAAAAaGzYGzMDzwMMGmZYMMLLzgNAAAAAYAA
shadowlands.soleahs_secret_technique_type_override=haste

# Default consumables
potion=tempered_potion_3
flask=flask_of_alchemical_chaos_3
food=the_sushi_special
augmentation=crystallized
temporary_enchant=main_hand:ironclaw_whetstone_3

# This default action priority list is automatically created based on your character.
# It is a attempt to provide you with a action list that is both simple and practicable,
# while resulting in a meaningful and good simulation. It may not result in the absolutely highest possible dps.
# Feel free to edit, adapt and improve it to your own needs.
# SimulationCraft is always looking for updates and improvements to the default action lists.

# Executed before combat begins. Accepts non-harmful actions only.
actions.precombat=summon_pet
# Snapshot raid buffed stats before combat begins.
actions.precombat+=/snapshot_stats
# Determine which trinket would make for the strongest cooldown sync. In descending priority: buff effects > damage effects, longer > shorter cooldowns, longer > shorter cast times.
actions.precombat+=/variable,name=stronger_trinket_slot,op=setif,value=1,value_else=2,condition=!trinket.2.is.house_of_cards&(trinket.1.is.house_of_cards|!trinket.2.has_cooldown|trinket.1.has_use_buff&(!trinket.2.has_use_buff|trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration)|!trinket.1.has_use_buff&(!trinket.2.has_use_buff&(trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration)))

# Executed every time the actor is available.
actions=auto_attack
actions+=/call_action_list,name=cds
actions+=/call_action_list,name=trinkets
actions+=/call_action_list,name=plst,if=active_enemies<3&talent.howl_of_the_pack_leader
actions+=/call_action_list,name=plcleave,if=active_enemies>2&talent.howl_of_the_pack_leader
actions+=/call_action_list,name=sentst,if=active_enemies<3&!talent.howl_of_the_pack_leader
actions+=/call_action_list,name=sentcleave,if=active_enemies>2&!talent.howl_of_the_pack_leader
# simply fires off if there is absolutely nothing else to press.
actions+=/arcane_torrent
actions+=/bag_of_tricks
actions+=/lights_judgment

# COOLDOWNS ACTIONLIST
actions.cds=blood_fury,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/invoke_external_buff,name=power_infusion,if=(buff.coordinated_assault.up&buff.coordinated_assault.remains>7&!buff.power_infusion.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault)
actions.cds+=/harpoon,if=prev.kill_command
actions.cds+=/ancestral_call,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/fireblood,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/berserking,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault|time_to_die<13
actions.cds+=/muzzle
actions.cds+=/potion,if=target.time_to_die<25|buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/use_item,use_off_gcd=1,slot=trinket1,if=buff.coordinated_assault.up&trinket.1.has_use_buff|cooldown.coordinated_assault.remains>31|!trinket.1.has_use_buff&cooldown.coordinated_assault.remains>20|time_to_die<cooldown.coordinated_assault.remains
actions.cds+=/use_item,use_off_gcd=1,slot=trinket2,if=buff.coordinated_assault.up&trinket.2.has_use_buff|cooldown.coordinated_assault.remains>31|!trinket.2.has_use_buff&cooldown.coordinated_assault.remains>20|time_to_die<cooldown.coordinated_assault.remains
actions.cds+=/aspect_of_the_eagle,if=target.distance>=6
actions.cds+=/use_item,name=spellstrike_warplance

# PACK LEADER | AOE ACTIONLIST
actions.plcleave=spearhead,if=cooldown.coordinated_assault.remains
actions.plcleave+=/kill_command,if=buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1
actions.plcleave+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/wildfire_bomb,if=cooldown.wildfire_bomb.charges_fractional>1.7
actions.plcleave+=/explosive_shot,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/raptor_bite,target_if=max:dot.serpent_sting.remains,if=buff.strike_it_rich.up&buff.strike_it_rich.remains<gcd|buff.hogstrider.remains&boar_charge.remains>0|buff.hogstrider.remains<gcd&buff.hogstrider.up|buff.hogstrider.remains&buff.strike_it_rich.remains|raid_event.adds.exists&raid_event.adds.remains<4
actions.plcleave+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/kill_command,if=(buff.howl_of_the_pack_leader_wyvern.remains|buff.howl_of_the_pack_leader_boar.remains|buff.howl_of_the_pack_leader_bear.remains)
actions.plcleave+=/flanking_strike,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/butchery,if=cooldown.wildfire_bomb.charges_fractional<1.5
actions.plcleave+=/coordinated_assault,if=buff.howl_of_the_pack_leader_cooldown.remains-buff.lead_from_the_front.duration<buff.lead_from_the_front.duration%gcd*0.6
actions.plcleave+=/kill_command,if=focus+cast_regen<focus.max
actions.plcleave+=/explosive_shot
actions.plcleave+=/kill_shot,if=buff.deathblow.remains&talent.sic_em
actions.plcleave+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!talent.contagious_reagents
actions.plcleave+=/raptor_bite,target_if=max:dot.serpent_sting.remains

# PACK LEADER | SINGLE TARGET ACTIONLIST.
actions.plst=kill_command,if=(buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1)|(buff.howl_of_the_pack_leader_wyvern.remains|buff.howl_of_the_pack_leader_boar.remains|buff.howl_of_the_pack_leader_bear.remains)
actions.plst+=/spearhead,if=cooldown.coordinated_assault.remains
actions.plst+=/flanking_strike,if=buff.tip_of_the_spear.stack>0&(cooldown.spearhead.remains>5|!talent.spearhead&cooldown.coordinated_assault.remains>5)
actions.plst+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!dot.serpent_sting.ticking&target.time_to_die>12&(!talent.contagious_reagents|active_dot.serpent_sting=0)
actions.plst+=/raptor_bite,target_if=max:dot.serpent_sting.remains,if=talent.contagious_reagents&active_dot.serpent_sting<active_enemies&dot.serpent_sting.remains
actions.plst+=/kill_command,if=buff.strike_it_rich.remains&buff.tip_of_the_spear.stack<1
actions.plst+=/raptor_bite,if=buff.strike_it_rich.remains&buff.tip_of_the_spear.stack>0
actions.plst+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0&(!raid_event.adds.exists|raid_event.adds.exists&raid_event.adds.in>40)
actions.plst+=/coordinated_assault,if=buff.howl_of_the_pack_leader_cooldown.remains-buff.lead_from_the_front.duration<buff.lead_from_the_front.duration%gcd*0.6|time_to_die<20|!talent.spearhead
actions.plst+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.plst+=/raptor_bite,target_if=max:dot.serpent_sting.remains,if=buff.howl_of_the_pack_leader_cooldown.up&buff.howl_of_the_pack_leader_cooldown.remains<2*gcd
actions.plst+=/kill_command,if=focus+cast_regen<focus.max&(!buff.relentless_primal_ferocity.up|(buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<2|focus<30))
actions.plst+=/explosive_shot,if=active_enemies>1
actions.plst+=/kill_shot,if=talent.cull_the_herd
actions.plst+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!talent.contagious_reagents
actions.plst+=/raptor_bite,target_if=max:dot.serpent_sting.remains
actions.plst+=/kill_shot
actions.plst+=/explosive_shot

# SENTINEL | DEFAULT AOE ACTIONLIST
actions.sentcleave=wildfire_bomb,if=!buff.lunar_storm_cooldown.remains
actions.sentcleave+=/kill_command,if=buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1
actions.sentcleave+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0|cooldown.wildfire_bomb.charges_fractional>1.9|(talent.bombardier&cooldown.coordinated_assault.remains<2*gcd)|talent.butchery&cooldown.butchery.remains<gcd
actions.sentcleave+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0
actions.sentcleave+=/raptor_bite,target_if=max:dot.serpent_sting.remains,if=buff.strike_it_rich.up&buff.strike_it_rich.remains<gcd
actions.sentcleave+=/butchery
actions.sentcleave+=/explosive_shot,if=buff.tip_of_the_spear.stack>0
actions.sentcleave+=/coordinated_assault
actions.sentcleave+=/flanking_strike,if=(buff.tip_of_the_spear.stack=2|buff.tip_of_the_spear.stack=1)
actions.sentcleave+=/kill_command,if=focus+cast_regen<focus.max
actions.sentcleave+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.sentcleave+=/explosive_shot
actions.sentcleave+=/kill_shot,if=buff.deathblow.remains&talent.sic_em
actions.sentcleave+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!talent.contagious_reagents
actions.sentcleave+=/raptor_bite,target_if=max:dot.serpent_sting.remains

# SENTINEL | DEFAULT SINGLE TARGET ACTIONLIST.
actions.sentst=wildfire_bomb,if=!buff.lunar_storm_cooldown.remains&buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,if=(buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1)
actions.sentst+=/spearhead,if=cooldown.coordinated_assault.remains
actions.sentst+=/flanking_strike,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,if=buff.strike_it_rich.remains&buff.tip_of_the_spear.stack<1
actions.sentst+=/mongoose_bite,if=buff.strike_it_rich.remains&buff.coordinated_assault.up
actions.sentst+=/wildfire_bomb,if=cooldown.wildfire_bomb.charges_fractional>1.7
actions.sentst+=/butchery
actions.sentst+=/coordinated_assault,if=!talent.bombardier|talent.bombardier&cooldown.wildfire_bomb.charges_fractional<2
actions.sentst+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,if=buff.tip_of_the_spear.stack<1&cooldown.flanking_strike.remains<gcd
actions.sentst+=/kill_command,if=focus+cast_regen<focus.max&(!buff.relentless_primal_ferocity.up|(buff.relentless_primal_ferocity.up&(buff.tip_of_the_spear.stack<1|focus<30)))
actions.sentst+=/mongoose_bite,if=buff.mongoose_fury.remains<gcd&buff.mongoose_fury.stack>0
actions.sentst+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/explosive_shot
actions.sentst+=/mongoose_bite,if=buff.mongoose_fury.remains
actions.sentst+=/kill_shot
actions.sentst+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!talent.contagious_reagents
actions.sentst+=/raptor_bite,target_if=max:dot.serpent_sting.remains

# True if effects that are desirable to sync a trinket buff with are ready.
actions.trinkets=variable,name=buff_sync_ready,value=buff.coordinated_assault.up
# Time until the effects that are desirable to sync a trinket buff with will be ready.
actions.trinkets+=/variable,name=buff_sync_remains,value=cooldown.coordinated_assault.remains
# True if effecs that are desirable to sync a trinket buff with are active.
actions.trinkets+=/variable,name=buff_sync_active,value=buff.coordinated_assault.up
# True if effects that are desirable to sync trinket damage with are active.
actions.trinkets+=/variable,name=damage_sync_active,value=1
# Time until the effects that are desirable to sync trinket damage with will be ready.
actions.trinkets+=/variable,name=damage_sync_remains,value=0
# Uses buff effect trinkets with cooldowns and is willing to delay usage up to 1/3 the trinket cooldown if it won't lose a usage in the fight. Fills in downtime with weaker buff effects if they won't also be saved for later cooldowns (happens if it won't delay over 1/3 the trinket cooldown and a stronger trinket won't be up in time) or damage effects if they won't inferfere with any buff effect usage.
actions.trinkets+=/use_items,slots=trinket1:trinket2,if=this_trinket.has_use_buff&(variable.buff_sync_ready&(variable.stronger_trinket_slot=this_trinket_slot|other_trinket.cooldown.remains)|!variable.buff_sync_ready&(variable.stronger_trinket_slot=this_trinket_slot&(variable.buff_sync_remains>this_trinket.cooldown.duration%3&fight_remains>this_trinket.cooldown.duration+20|other_trinket.has_use_buff&other_trinket.cooldown.remains>variable.buff_sync_remains-15&other_trinket.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains+45>fight_remains)|variable.stronger_trinket_slot!=this_trinket_slot&(other_trinket.cooldown.remains&(other_trinket.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains>=20|other_trinket.cooldown.remains-5>=variable.buff_sync_remains&(variable.buff_sync_remains>this_trinket.cooldown.duration%3|this_trinket.cooldown.duration<fight_remains&(variable.buff_sync_remains+this_trinket.cooldown.duration>fight_remains)))|other_trinket.cooldown.ready&variable.buff_sync_remains>20&variable.buff_sync_remains<other_trinket.cooldown.duration%3)))|!this_trinket.has_use_buff&(this_trinket.cast_time=0|!variable.buff_sync_active)&(!this_trinket.is.junkmaestros_mega_magnet|buff.junkmaestros_mega_magnet.stack>10)&(!other_trinket.has_cooldown&(variable.damage_sync_active|this_trinket.is.junkmaestros_mega_magnet&buff.junkmaestros_mega_magnet.stack>25|!this_trinket.is.junkmaestros_mega_magnet&variable.damage_sync_remains>this_trinket.cooldown.duration%3)|other_trinket.has_cooldown&(!other_trinket.has_use_buff&(variable.stronger_trinket_slot=this_trinket_slot|other_trinket.cooldown.remains)&(variable.damage_sync_active|this_trinket.is.junkmaestros_mega_magnet&buff.junkmaestros_mega_magnet.stack>25|variable.damage_sync_remains>this_trinket.cooldown.duration%3&!this_trinket.is.junkmaestros_mega_magnet|other_trinket.cooldown.remains-5<variable.damage_sync_remains&variable.damage_sync_remains>=20)|other_trinket.has_use_buff&(variable.damage_sync_active|this_trinket.is.junkmaestros_mega_magnet&buff.junkmaestros_mega_magnet.stack>25|!this_trinket.is.junkmaestros_mega_magnet&variable.damage_sync_remains>this_trinket.cooldown.duration%3)&(other_trinket.cooldown.remains>=20|other_trinket.cooldown.remains-5>variable.buff_sync_remains)))|fight_remains<25&(variable.stronger_trinket_slot=this_trinket_slot|other_trinket.cooldown.remains)

head=midnight_heralds_cowl,id=237646,bonus_id=6652/12921/10390/12231/12353/1514/10255/12676
neck=wastelanders_gilded_pendant,id=243499,bonus_id=6652/12289/3220/10255/10879/10396,gem_id=213464/213464
shoulders=midnight_heralds_shadowguards,id=237644,bonus_id=6652/10390/12233/12675/12353/1514/10255
back=reshii_wraps,id=235499,bonus_id=12401/9893,gem_id=238045,enchant_id=7409
chest=midnight_heralds_hauberk,id=237649,bonus_id=10354/12229/6652/12676/12297/1514/10255,enchant_id=7364
shirt=lucky_shirt,id=138385
tabard=renowned_guild_tabard,id=69210
wrists=consecrated_barons_bindings,id=221124,bonus_id=12352/10390/6652/12921/12239/10383/3193/10255,enchant_id=7397
hands=gloves_of_whispering_winds,id=156317,bonus_id=7756/12239/10383/12296/11406/10255
waist=durable_information_securing_container,id=245965,bonus_id=12533/1489,gem_id=213455,titan_disc_id=1236275
legs=midnight_heralds_petticoat,id=237645,bonus_id=12232/6652/12676/12289/1501/10255,enchant_id=7601
feet=boots_of_unsettled_prey,id=156467,bonus_id=8902/7756/12239/10383/12351/11402/10255,enchant_id=7418
finger1=devout_zealots_ring,id=221136,bonus_id=12352/10390/6652/10383/3193/10255/10879/10396,gem_id=213455/213455,enchant_id=7334
finger2=radiant_necromancers_band,id=221200,bonus_id=10390/6652/10383/12361/3215/10255/10879/10396,gem_id=213458/213458,enchant_id=7470
trinket1=signet_of_the_priory,id=219308,bonus_id=10390/40/10383/12353/3196/10255
trinket2=improvised_seaforium_pacemaker,id=232541,bonus_id=10390/6652/10383/12376/1520/10255
main_hand=greasemonkeys_shiftstick,id=228892,bonus_id=6652/10356/12376/1533/10255

# Gear Summary
# gear_ilvl=701.33
# gear_agility=68636
# gear_stamina=524708
# gear_attack_power=469
# gear_crit_rating=18030
# gear_haste_rating=14759
# gear_mastery_rating=10058
# gear_versatility_rating=5156
# gear_leech_rating=1020
# gear_speed_rating=1250
# gear_avoidance_rating=842
# gear_armor=54831
# set_bonus=name=thewarwithin_season_3,pc=2,hero_tree=sentinel,enable=1
# set_bonus=name=thewarwithin_season_3,pc=4,hero_tree=sentinel,enable=1

Simulation & Raid Information
Raw Ability Summary
Dummy_Enemy_1 : 0 dps, 0 dps to main target
Dummy_Enemy_2 : 0 dps, 0 dps to main target
Dummy_Enemy_3 : 0 dps, 0 dps to main target
Dummy_Enemy_4 : 0 dps, 0 dps to main target
Fluffy_Pillow : 0 dps, 0 dps to main target