hunter="哈士奇的远征"
source=default
spec=survival
level=80
race=human
role=attack
position=back
professions=leatherworking=69/engineering=27
talents=C8PA11Mk8aZ38kAf+zso3nZ9IMGYglxoxyAysFsNzYZmZmZmhxMzMjxYMzYWAAAAAAAaGzYGzMDzwMMGmZYMMLLzgNAAAAAYAA
shadowlands.soleahs_secret_technique_type_override=haste

# Default consumables
potion=tempered_potion_3
flask=flask_of_alchemical_chaos_3
food=the_sushi_special
augmentation=crystallized
temporary_enchant=main_hand:ironclaw_whetstone_3

# This default action priority list is automatically created based on your character.
# It is a attempt to provide you with a action list that is both simple and practicable,
# while resulting in a meaningful and good simulation. It may not result in the absolutely highest possible dps.
# Feel free to edit, adapt and improve it to your own needs.
# SimulationCraft is always looking for updates and improvements to the default action lists.

# Executed before combat begins. Accepts non-harmful actions only.
actions.precombat=summon_pet
# Snapshot raid buffed stats before combat begins.
actions.precombat+=/snapshot_stats
# Determine which trinket would make for the strongest cooldown sync. In descending priority: buff effects > damage effects, longer > shorter cooldowns, longer > shorter cast times.
actions.precombat+=/variable,name=stronger_trinket_slot,op=setif,value=1,value_else=2,condition=!trinket.2.is.house_of_cards&(trinket.1.is.house_of_cards|!trinket.2.has_cooldown|trinket.1.has_use_buff&(!trinket.2.has_use_buff|trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration)|!trinket.1.has_use_buff&(!trinket.2.has_use_buff&(trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration)))

# Executed every time the actor is available.
actions=auto_attack
actions+=/call_action_list,name=cds
actions+=/call_action_list,name=trinkets
actions+=/call_action_list,name=plst,if=active_enemies<3&talent.howl_of_the_pack_leader
actions+=/call_action_list,name=plcleave,if=active_enemies>2&talent.howl_of_the_pack_leader
actions+=/call_action_list,name=sentst,if=active_enemies<3&!talent.howl_of_the_pack_leader
actions+=/call_action_list,name=sentcleave,if=active_enemies>2&!talent.howl_of_the_pack_leader
# simply fires off if there is absolutely nothing else to press.
actions+=/arcane_torrent
actions+=/bag_of_tricks
actions+=/lights_judgment

# COOLDOWNS ACTIONLIST
actions.cds=blood_fury,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/invoke_external_buff,name=power_infusion,if=(buff.coordinated_assault.up&buff.coordinated_assault.remains>7&!buff.power_infusion.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault)
actions.cds+=/harpoon,if=prev.kill_command
actions.cds+=/ancestral_call,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/fireblood,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/berserking,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault|time_to_die<13
actions.cds+=/muzzle
actions.cds+=/potion,if=target.time_to_die<25|buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/use_item,use_off_gcd=1,slot=trinket1,if=buff.coordinated_assault.up&trinket.1.has_use_buff|cooldown.coordinated_assault.remains>31|!trinket.1.has_use_buff&cooldown.coordinated_assault.remains>20|time_to_die<cooldown.coordinated_assault.remains
actions.cds+=/use_item,use_off_gcd=1,slot=trinket2,if=buff.coordinated_assault.up&trinket.2.has_use_buff|cooldown.coordinated_assault.remains>31|!trinket.2.has_use_buff&cooldown.coordinated_assault.remains>20|time_to_die<cooldown.coordinated_assault.remains
actions.cds+=/aspect_of_the_eagle,if=target.distance>=6
actions.cds+=/use_item,name=spellstrike_warplance

# PACK LEADER | AOE ACTIONLIST
actions.plcleave=spearhead,if=cooldown.coordinated_assault.remains
actions.plcleave+=/kill_command,if=buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1
actions.plcleave+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/wildfire_bomb,if=cooldown.wildfire_bomb.charges_fractional>1.7
actions.plcleave+=/explosive_shot,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/raptor_bite,target_if=max:dot.serpent_sting.remains,if=buff.strike_it_rich.up&buff.strike_it_rich.remains<gcd|buff.hogstrider.remains&boar_charge.remains>0|buff.hogstrider.remains<gcd&buff.hogstrider.up|buff.hogstrider.remains&buff.strike_it_rich.remains|raid_event.adds.exists&raid_event.adds.remains<4
actions.plcleave+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/kill_command,if=(buff.howl_of_the_pack_leader_wyvern.remains|buff.howl_of_the_pack_leader_boar.remains|buff.howl_of_the_pack_leader_bear.remains)
actions.plcleave+=/flanking_strike,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/butchery,if=cooldown.wildfire_bomb.charges_fractional<1.5
actions.plcleave+=/coordinated_assault,if=buff.howl_of_the_pack_leader_cooldown.remains-buff.lead_from_the_front.duration<buff.lead_from_the_front.duration%gcd*0.6
actions.plcleave+=/kill_command,if=focus+cast_regen<focus.max
actions.plcleave+=/explosive_shot
actions.plcleave+=/kill_shot,if=buff.deathblow.remains&talent.sic_em
actions.plcleave+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!talent.contagious_reagents
actions.plcleave+=/raptor_bite,target_if=max:dot.serpent_sting.remains

# PACK LEADER | SINGLE TARGET ACTIONLIST.
actions.plst=kill_command,if=(buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1)|(buff.howl_of_the_pack_leader_wyvern.remains|buff.howl_of_the_pack_leader_boar.remains|buff.howl_of_the_pack_leader_bear.remains)
actions.plst+=/spearhead,if=cooldown.coordinated_assault.remains
actions.plst+=/flanking_strike,if=buff.tip_of_the_spear.stack>0&(cooldown.spearhead.remains>5|!talent.spearhead&cooldown.coordinated_assault.remains>5)
actions.plst+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!dot.serpent_sting.ticking&target.time_to_die>12&(!talent.contagious_reagents|active_dot.serpent_sting=0)
actions.plst+=/raptor_bite,target_if=max:dot.serpent_sting.remains,if=talent.contagious_reagents&active_dot.serpent_sting<active_enemies&dot.serpent_sting.remains
actions.plst+=/kill_command,if=buff.strike_it_rich.remains&buff.tip_of_the_spear.stack<1
actions.plst+=/raptor_bite,if=buff.strike_it_rich.remains&buff.tip_of_the_spear.stack>0
actions.plst+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0&(!raid_event.adds.exists|raid_event.adds.exists&raid_event.adds.in>40)
actions.plst+=/coordinated_assault,if=buff.howl_of_the_pack_leader_cooldown.remains-buff.lead_from_the_front.duration<buff.lead_from_the_front.duration%gcd*0.6|time_to_die<20|!talent.spearhead
actions.plst+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.plst+=/raptor_bite,target_if=max:dot.serpent_sting.remains,if=buff.howl_of_the_pack_leader_cooldown.up&buff.howl_of_the_pack_leader_cooldown.remains<2*gcd
actions.plst+=/kill_command,if=focus+cast_regen<focus.max&(!buff.relentless_primal_ferocity.up|(buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<2|focus<30))
actions.plst+=/explosive_shot,if=active_enemies>1
actions.plst+=/kill_shot,if=talent.cull_the_herd
actions.plst+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!talent.contagious_reagents
actions.plst+=/raptor_bite,target_if=max:dot.serpent_sting.remains
actions.plst+=/kill_shot
actions.plst+=/explosive_shot

# SENTINEL | DEFAULT AOE ACTIONLIST
actions.sentcleave=wildfire_bomb,if=!buff.lunar_storm_cooldown.remains
actions.sentcleave+=/kill_command,if=buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1
actions.sentcleave+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0|cooldown.wildfire_bomb.charges_fractional>1.9|(talent.bombardier&cooldown.coordinated_assault.remains<2*gcd)|talent.butchery&cooldown.butchery.remains<gcd
actions.sentcleave+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0
actions.sentcleave+=/raptor_bite,target_if=max:dot.serpent_sting.remains,if=buff.strike_it_rich.up&buff.strike_it_rich.remains<gcd
actions.sentcleave+=/butchery
actions.sentcleave+=/explosive_shot,if=buff.tip_of_the_spear.stack>0
actions.sentcleave+=/coordinated_assault
actions.sentcleave+=/flanking_strike,if=(buff.tip_of_the_spear.stack=2|buff.tip_of_the_spear.stack=1)
actions.sentcleave+=/kill_command,if=focus+cast_regen<focus.max
actions.sentcleave+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.sentcleave+=/explosive_shot
actions.sentcleave+=/kill_shot,if=buff.deathblow.remains&talent.sic_em
actions.sentcleave+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!talent.contagious_reagents
actions.sentcleave+=/raptor_bite,target_if=max:dot.serpent_sting.remains

# SENTINEL | DEFAULT SINGLE TARGET ACTIONLIST.
actions.sentst=wildfire_bomb,if=!buff.lunar_storm_cooldown.remains&buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,if=(buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1)
actions.sentst+=/spearhead,if=cooldown.coordinated_assault.remains
actions.sentst+=/flanking_strike,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,if=buff.strike_it_rich.remains&buff.tip_of_the_spear.stack<1
actions.sentst+=/mongoose_bite,if=buff.strike_it_rich.remains&buff.coordinated_assault.up
actions.sentst+=/wildfire_bomb,if=cooldown.wildfire_bomb.charges_fractional>1.7
actions.sentst+=/butchery
actions.sentst+=/coordinated_assault,if=!talent.bombardier|talent.bombardier&cooldown.wildfire_bomb.charges_fractional<2
actions.sentst+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,if=buff.tip_of_the_spear.stack<1&cooldown.flanking_strike.remains<gcd
actions.sentst+=/kill_command,if=focus+cast_regen<focus.max&(!buff.relentless_primal_ferocity.up|(buff.relentless_primal_ferocity.up&(buff.tip_of_the_spear.stack<1|focus<30)))
actions.sentst+=/mongoose_bite,if=buff.mongoose_fury.remains<gcd&buff.mongoose_fury.stack>0
actions.sentst+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/explosive_shot
actions.sentst+=/mongoose_bite,if=buff.mongoose_fury.remains
actions.sentst+=/kill_shot
actions.sentst+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!talent.contagious_reagents
actions.sentst+=/raptor_bite,target_if=max:dot.serpent_sting.remains

# True if effects that are desirable to sync a trinket buff with are ready.
actions.trinkets=variable,name=buff_sync_ready,value=buff.coordinated_assault.up
# Time until the effects that are desirable to sync a trinket buff with will be ready.
actions.trinkets+=/variable,name=buff_sync_remains,value=cooldown.coordinated_assault.remains
# True if effecs that are desirable to sync a trinket buff with are active.
actions.trinkets+=/variable,name=buff_sync_active,value=buff.coordinated_assault.up
# True if effects that are desirable to sync trinket damage with are active.
actions.trinkets+=/variable,name=damage_sync_active,value=1
# Time until the effects that are desirable to sync trinket damage with will be ready.
actions.trinkets+=/variable,name=damage_sync_remains,value=0
# Uses buff effect trinkets with cooldowns and is willing to delay usage up to 1/3 the trinket cooldown if it won't lose a usage in the fight. Fills in downtime with weaker buff effects if they won't also be saved for later cooldowns (happens if it won't delay over 1/3 the trinket cooldown and a stronger trinket won't be up in time) or damage effects if they won't inferfere with any buff effect usage.
actions.trinkets+=/use_items,slots=trinket1:trinket2,if=this_trinket.has_use_buff&(variable.buff_sync_ready&(variable.stronger_trinket_slot=this_trinket_slot|other_trinket.cooldown.remains)|!variable.buff_sync_ready&(variable.stronger_trinket_slot=this_trinket_slot&(variable.buff_sync_remains>this_trinket.cooldown.duration%3&fight_remains>this_trinket.cooldown.duration+20|other_trinket.has_use_buff&other_trinket.cooldown.remains>variable.buff_sync_remains-15&other_trinket.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains+45>fight_remains)|variable.stronger_trinket_slot!=this_trinket_slot&(other_trinket.cooldown.remains&(other_trinket.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains>=20|other_trinket.cooldown.remains-5>=variable.buff_sync_remains&(variable.buff_sync_remains>this_trinket.cooldown.duration%3|this_trinket.cooldown.duration<fight_remains&(variable.buff_sync_remains+this_trinket.cooldown.duration>fight_remains)))|other_trinket.cooldown.ready&variable.buff_sync_remains>20&variable.buff_sync_remains<other_trinket.cooldown.duration%3)))|!this_trinket.has_use_buff&(this_trinket.cast_time=0|!variable.buff_sync_active)&(!this_trinket.is.junkmaestros_mega_magnet|buff.junkmaestros_mega_magnet.stack>10)&(!other_trinket.has_cooldown&(variable.damage_sync_active|this_trinket.is.junkmaestros_mega_magnet&buff.junkmaestros_mega_magnet.stack>25|!this_trinket.is.junkmaestros_mega_magnet&variable.damage_sync_remains>this_trinket.cooldown.duration%3)|other_trinket.has_cooldown&(!other_trinket.has_use_buff&(variable.stronger_trinket_slot=this_trinket_slot|other_trinket.cooldown.remains)&(variable.damage_sync_active|this_trinket.is.junkmaestros_mega_magnet&buff.junkmaestros_mega_magnet.stack>25|variable.damage_sync_remains>this_trinket.cooldown.duration%3&!this_trinket.is.junkmaestros_mega_magnet|other_trinket.cooldown.remains-5<variable.damage_sync_remains&variable.damage_sync_remains>=20)|other_trinket.has_use_buff&(variable.damage_sync_active|this_trinket.is.junkmaestros_mega_magnet&buff.junkmaestros_mega_magnet.stack>25|!this_trinket.is.junkmaestros_mega_magnet&variable.damage_sync_remains>this_trinket.cooldown.duration%3)&(other_trinket.cooldown.remains>=20|other_trinket.cooldown.remains-5>variable.buff_sync_remains)))|fight_remains<25&(variable.stronger_trinket_slot=this_trinket_slot|other_trinket.cooldown.remains)

head=midnight_heralds_cowl,id=237646,bonus_id=6652/12921/10390/12231/12353/1514/10255/12676
neck=wastelanders_gilded_pendant,id=243499,bonus_id=6652/12289/3220/10255/10879/10396,gem_id=213464/213464
shoulders=midnight_heralds_shadowguards,id=237644,bonus_id=6652/10390/12233/12675/12353/1514/10255
back=reshii_wraps,id=235499,bonus_id=12401/9893,gem_id=238045,enchant_id=7409
chest=midnight_heralds_hauberk,id=237649,bonus_id=10354/12229/6652/12676/12297/1514/10255,enchant_id=7364
shirt=lucky_shirt,id=138385
tabard=renowned_guild_tabard,id=69210
wrists=consecrated_barons_bindings,id=221124,bonus_id=12352/10390/6652/12921/12239/10383/3193/10255,enchant_id=7397
hands=gloves_of_whispering_winds,id=156317,bonus_id=7756/12239/10383/12296/11406/10255
waist=durable_information_securing_container,id=245965,bonus_id=12533/1489,gem_id=213455,titan_disc_id=1236275
legs=midnight_heralds_petticoat,id=237645,bonus_id=12232/6652/12676/12289/1501/10255,enchant_id=7601
feet=boots_of_unsettled_prey,id=156467,bonus_id=8902/7756/12239/10383/12351/11402/10255,enchant_id=7418
finger1=devout_zealots_ring,id=221136,bonus_id=12352/10390/6652/10383/3193/10255/10879/10396,gem_id=213455/213455,enchant_id=7334
finger2=radiant_necromancers_band,id=221200,bonus_id=10390/6652/10383/12361/3215/10255/10879/10396,gem_id=213458/213458,enchant_id=7470
trinket1=signet_of_the_priory,id=219308,bonus_id=10390/40/10383/12353/3196/10255
trinket2=improvised_seaforium_pacemaker,id=232541,bonus_id=10390/6652/10383/12376/1520/10255
main_hand=greasemonkeys_shiftstick,id=228892,bonus_id=6652/10356/12376/1533/10255

# Gear Summary
# gear_ilvl=701.33
# gear_agility=68636
# gear_stamina=524708
# gear_attack_power=469
# gear_crit_rating=18030
# gear_haste_rating=14759
# gear_mastery_rating=10058
# gear_versatility_rating=5156
# gear_leech_rating=1020
# gear_speed_rating=1250
# gear_avoidance_rating=842
# gear_armor=54831
# set_bonus=name=thewarwithin_season_3,pc=2,hero_tree=sentinel,enable=1
# set_bonus=name=thewarwithin_season_3,pc=4,hero_tree=sentinel,enable=1