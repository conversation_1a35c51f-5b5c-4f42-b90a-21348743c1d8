
## Date: 2025-08-16-23:05:05

actions.precombat+=/summon_pet,if=talent.unbreakable_bond
# Determine the stronger trinket to sync with cooldowns. In descending priority: buff effects > damage effects, longer > shorter cooldowns, longer > shorter cast times. Special case to consider Mirror of Fractured Tomorrows weaker than other buff effects since its power is split between the dmg effect and the buff effect.
actions.precombat+=/variable,name=trinket_1_stronger,value=!trinket.2.has_cooldown|trinket.1.has_use_buff&(!trinket.2.has_use_buff|!trinket.1.is.mirror_of_fractured_tomorrows&(trinket.2.is.mirror_of_fractured_tomorrows|trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration))|!trinket.1.has_use_buff&(!trinket.2.has_use_buff&(trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration))
actions.precombat+=/variable,name=trinket_2_stronger,value=!variable.trinket_1_stronger
actions.precombat+=/variable,name=pet_healing,value=40
# 集中值管理变量优化
actions.precombat+=/variable,name=focus_regen_multiplier,value=1.0+stat.haste_rating%32500
actions.precombat+=/variable,name=aimed_shot_focus_cost,value=35*(1-0.5*buff.trueshot.up)

# BUFF窗口管理变量
actions.precombat+=/variable,name=perfect_shot_window,value=buff.trueshot.up&buff.bulletstorm.up&(buff.lunar_storm.up|!hero_tree.sentinel)
actions.precombat+=/variable,name=burst_phase,value=buff.trueshot.up|(buff.lunar_storm.up&hero_tree.sentinel)
actions.precombat+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20
actions.precombat+=/aimed_shot,if=active_enemies<3|talent.black_arrow&talent.headshot
actions.precombat+=/steady_shot

actions+=/counter_shot
actions+=/tranquilizing_shot
# Determine if it is a good time to use Trueshot. Raid event optimization takes priority so usage is saved for multiple targets as long as it won't delay over half its duration. Otherwise allow for small delays to line up buff effect trinkets, and when using Bullseye, delay the last usage of the fight for max stacks.
actions+=/variable,name=trueshot_ready,value=cooldown.trueshot.ready&(!talent.bullseye|fight_remains>cooldown.trueshot.duration_guess+buff.trueshot.duration%2|buff.bullseye.stack=buff.bullseye.max_stack)&(!trinket.1.has_use_buff|trinket.1.cooldown.remains>30|trinket.1.cooldown.ready)&(!trinket.2.has_use_buff|trinket.2.cooldown.remains>30|trinket.2.cooldown.ready)|boss&fight_remains<25
actions+=/mend_pet,if=pet.health_pct<pet_healing
actions+=/call_action_list,name=cds
actions+=/call_action_list,name=trinkets
actions+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20
actions+=/call_action_list,name=trickshots,strict=1,if=active_enemies>2&talent.trick_shots
actions+=/call_action_list,name=cleave,strict=1,if=active_enemies>1
actions+=/call_action_list,name=st

## actions.cds+=/invoke_external_buff,name=power_infusion,if=buff.trueshot.remains>12|fight_remains<13
actions.cds+=/berserking,if=buff.trueshot.up|boss&fight_remains<13
actions.cds+=/blood_fury,if=buff.trueshot.up|cooldown.trueshot.remains>30|boss&fight_remains<16
actions.cds+=/ancestral_call,if=buff.trueshot.up|cooldown.trueshot.remains>30|boss&fight_remains<16
actions.cds+=/fireblood,if=buff.trueshot.up|cooldown.trueshot.remains>30|boss&fight_remains<9
actions.cds+=/lights_judgment,if=buff.trueshot.down
# 药水使用优化 - 与多BUFF同步（基于手法文件优化）
actions.cds+=/potion,if=variable.perfect_shot_window|(buff.trueshot.up&(buff.bloodlust.up|target.health.pct<20))|boss&fight_remains<31

# ################# 2 targets (2+ without Trick Shots) - 优化版（修复无效多重射击）
# 夺命射击最高优先级 - 斩杀阶段和BUFF期
actions.cleave+=/kill_shot,cycle_targets=1,if=talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)|!talent.headshot&buff.razor_fragments.up

# 黑蚀箭优先级 - 黑暗游侠天赋核心技能
actions.cleave+=/black_arrow,if=buff.precise_shots.up&buff.moving_target.down&variable.trueshot_ready
actions.cleave+=/black_arrow,if=talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)|!talent.headshot&buff.razor_fragments.up

# 百发百中开启 - 冷却好就用
actions.cleave+=/trueshot,if=variable.trueshot_ready&(buff.double_tap.down|!talent.volley)&(buff.lunar_storm_cooldown.up|!talent.double_tap|!talent.volley)&(buff.precise_shots.down|buff.moving_target.up|!talent.volley)

# 哨兵天赋月之风暴优化
actions.cleave+=/rapid_fire,if=talent.lunar_storm&buff.lunar_storm_cooldown.down&(buff.precise_shots.down|buff.moving_target.up|cooldown.volley.remains&cooldown.trueshot.remains|!talent.volley)
actions.cleave+=/aimed_shot,cycle_targets=1,if=hero_tree.sentinel&buff.lunar_storm.up&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&(buff.streamline.stack>=1|focus>=50)

# 爆炸射击 - 精确引爆连招
actions.cleave+=/explosive_shot,if=talent.precision_detonation&prev.1.aimed_shot&(buff.trueshot.down|!talent.windrunner_quiver)

# 乱射优先级 - 双目标有效AOE技能
actions.cleave+=/volley,if=(talent.double_tap&buff.double_tap.down|!talent.aspect_of_the_hydra)&(buff.precise_shots.down|buff.moving_target.up)
actions.cleave+=/volley,if=!talent.double_tap&(buff.precise_shots.down|buff.moving_target.up)

# 瞄准射击优先级 - 主要输出技能
actions.cleave+=/aimed_shot,cycle_targets=1,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&full_recharge_time<action.rapid_fire.execute_time+cast_time&(!talent.bulletstorm|buff.bulletstorm.up)&talent.windrunner_quiver
actions.cleave+=/aimed_shot,cycle_targets=1,if=buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up

# 急速射击优化 - BUFF维护
actions.cleave+=/rapid_fire,if=talent.bulletstorm&buff.bulletstorm.down&(!talent.double_tap|buff.double_tap.up|!talent.aspect_of_the_hydra&buff.trick_shots.remains>execute_time)&(buff.precise_shots.down|buff.moving_target.up|!talent.volley)
actions.cleave+=/rapid_fire,if=!talent.bulletstorm|buff.bulletstorm.stack<=10|talent.aspect_of_the_hydra
actions.cleave+=/rapid_fire

# 填充技能优化 - 移除无效多重射击，优先奥术射击
actions.cleave+=/arcane_shot,cycle_targets=1,if=buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)

# 爆炸射击常规使用
actions.cleave+=/explosive_shot,if=talent.precision_detonation|buff.trueshot.down

# 黑蚀箭常规使用
actions.cleave+=/black_arrow,if=!talent.headshot

# 集中值管理 - 特殊情况下的稳固射击
actions.cleave+=/steady_shot,if=talent.black_arrow&focus+cast_regen<focus.max&prev.1.aimed_shot&!buff.deathblow.react&buff.trueshot.down&cooldown.trueshot.remains

# 最后选择稳固射击
actions.cleave+=/steady_shot

# ########## 1个目标
# 黑蚀箭优先级提升 - 配合弹无虚发使用（基于手法文件优化）
actions.st+=/black_arrow,if=buff.precise_shots.stack>=2&hero_tree.dark_ranger
actions.st+=/explosive_shot,if=talent.precision_detonation&prev.1.aimed_shot&buff.trueshot.down
actions.st+=/volley,if=buff.double_tap.down&(!raid_event.adds.exists|raid_event.adds.in>cooldown)
# 百发百中开启前准备 - 确保黑蚀箭已使用（基于手法文件优化）
actions.st+=/trueshot,if=variable.trueshot_ready&buff.double_tap.down&(!hero_tree.dark_ranger|!talent.black_arrow|!buff.black_arrow.up)
# Queue Steady Shot after Aimed Shot if a Deathblow hasn't already been up long enough to be reacted to. Sentinel only seems to like this due to the Precise Shots gcd bug.
actions.st+=/steady_shot,if=talent.black_arrow&focus+cast_regen<focus.max&prev.1.aimed_shot&!buff.deathblow.react&buff.trueshot.down&cooldown.trueshot.remains
# 哨兵天赋月之风暴优化 - 优先触发并在BUFF期间多打瞄准（基于手法文件优化）
actions.st+=/rapid_fire,if=talent.lunar_storm&buff.lunar_storm_cooldown.down&hero_tree.sentinel&action.aimed_shot.charges>=1
# 奥术瞄准射击优化 - 月之风暴期间瞄准变为奥术伤害（基于手法文件优化）
actions.st+=/aimed_shot,if=hero_tree.sentinel&buff.lunar_storm.up&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&(buff.streamline.stack>=1|focus>=50)
# 哨兵天赋夺命射击优化 - 替代奥术射击消耗弹无虚发（基于手法文件优化）
actions.st+=/kill_shot,if=(talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)|!talent.headshot&buff.razor_fragments.up)|(hero_tree.sentinel&buff.precise_shots.up&target.health.pct>20)
actions.st+=/black_arrow,if=!talent.headshot|talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)
# 标记增伤优化 - 确保标记存在时打瞄准（基于手法文件优化）
actions.st+=/aimed_shot,if=debuff.spotters_mark.up&buff.moving_target.up&(buff.streamline.stack>=1|buff.trueshot.up)&focus>=variable.aimed_shot_focus_cost
# 弹无虚发BUFF优先级管理 - 优先留给黑蚀箭（基于手法文件优化）
actions.st+=/arcane_shot,if=buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)&(!hero_tree.dark_ranger|!talent.black_arrow|cooldown.black_arrow.remains>3)
# Prioritize Aimed Shot a little higher when close to capping charges.
actions.st+=/aimed_shot,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&full_recharge_time<action.rapid_fire.execute_time+cast_time&(!talent.bulletstorm|buff.bulletstorm.up)&talent.windrunner_quiver
# 百发百中期间优先瞄准射击（基于手法文件优化）
# 完美瞄准射击窗口 - 多BUFF叠加时优先（基于手法文件优化）
actions.st+=/aimed_shot,if=variable.perfect_shot_window&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&focus>=variable.aimed_shot_focus_cost&(buff.streamline.stack>=2|buff.trueshot.up)
actions.st+=/aimed_shot,if=buff.trueshot.up&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&focus>=variable.aimed_shot_focus_cost&!variable.perfect_shot_window
# 急速射击断条优化 - AOE环境下可断条（基于手法文件优化）
actions.st+=/rapid_fire,if=active_enemies>=3&buff.bulletstorm.stack<20&execute_time>1
actions.st+=/rapid_fire,if=(!buff.trueshot.up|focus<50)&(buff.bulletstorm.remains<3|!buff.bulletstorm.up)&active_enemies<3
# 行云流水BUFF优化 - 确保有BUFF再打瞄准（基于手法文件优化）
actions.st+=/aimed_shot,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&(buff.streamline.stack>=1|buff.trueshot.up)
# 爆炸射击连招优化 - 月之风暴期间的小连招（基于手法文件优化）
actions.st+=/explosive_shot,if=hero_tree.sentinel&buff.lunar_storm.up&buff.streamline.stack>=2&action.aimed_shot.charges>=1
actions.st+=/explosive_shot,if=!buff.trueshot.up&(talent.precision_detonation|cooldown.explosive_shot.ready)&!variable.burst_phase
# 集中值预判优化 - 提前回复集中值（基于手法文件优化）
actions.st+=/steady_shot,if=focus<variable.aimed_shot_focus_cost&action.aimed_shot.charges>=1&!buff.trueshot.up
actions.st+=/steady_shot,if=focus<60|focus.time_to_max<3

# ################ 3+ targets (with Trick Shots)
actions.trickshots+=/explosive_shot,if=talent.precision_detonation&prev.1.aimed_shot&buff.trueshot.down&(!talent.shrapnel_shot|buff.lock_and_load.down)
# 乱射优化 - AOE爆发期使用（基于手法文件优化）
actions.trickshots+=/volley,if=buff.double_tap.down&(!talent.shrapnel_shot|buff.lock_and_load.down)&(buff.trueshot.up|buff.lunar_storm.up)
actions.trickshots+=/volley,if=buff.double_tap.down&(!talent.shrapnel_shot|buff.lock_and_load.down)&!buff.trueshot.up&!buff.lunar_storm.up&cooldown.trueshot.remains>10
actions.trickshots+=/rapid_fire,if=talent.bulletstorm&buff.bulletstorm.down&buff.trick_shots.remains>execute_time
actions.trickshots+=/rapid_fire,if=hero_tree.sentinel&buff.lunar_storm_cooldown.down&buff.trick_shots.remains>execute_time
# Queue Steady Shot after Aimed Shot if a Deathblow hasn't already been up long enough to be reacted to.
actions.trickshots+=/steady_shot,if=talent.black_arrow&focus+cast_regen<focus.max&prev.1.aimed_shot&!buff.deathblow.react&buff.trueshot.down&cooldown.trueshot.remains
actions.trickshots+=/black_arrow,if=!talent.headshot|buff.precise_shots.up|buff.trick_shots.down
# Retarget to possibly spread an extra Spotter's Mark if able.
# 多重射击技巧射击优化 - 确保技巧射击BUFF不断（基于手法文件优化）
actions.trickshots+=/multishot,cycle_targets=1,if=buff.trick_shots.down|buff.trick_shots.remains<gcd.max
actions.trickshots+=/multishot,cycle_targets=1,if=buff.precise_shots.up&buff.moving_target.down&buff.trick_shots.up
actions.trickshots+=/trueshot,if=variable.trueshot_ready&buff.double_tap.down
actions.trickshots+=/volley,if=buff.double_tap.down&(!talent.salvo|!talent.precision_detonation|(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up))
# Prioritize Aimed Shot a little higher when close to capping charges.
actions.trickshots+=/aimed_shot,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&buff.trick_shots.up&buff.bulletstorm.up&full_recharge_time<gcd
actions.trickshots+=/rapid_fire,if=buff.trick_shots.remains>execute_time&(!talent.black_arrow|buff.deathblow.down)&(!talent.no_scope|debuff.spotters_mark.down)&(talent.no_scope|buff.bulletstorm.down)
actions.trickshots+=/explosive_shot,if=talent.precision_detonation&talent.shrapnel_shot&buff.lock_and_load.down&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)
actions.trickshots+=/aimed_shot,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&buff.trick_shots.up
actions.trickshots+=/explosive_shot,if=!talent.shrapnel_shot
actions.trickshots+=/steady_shot,if=focus+cast_regen<focus.max
actions.trickshots+=/multishot

actions.trinkets+=/variable,name=buff_sync_ready,value=cooldown.trueshot.ready
actions.trinkets+=/variable,name=buff_sync_remains,value=cooldown.trueshot.remains
actions.trinkets+=/variable,name=buff_sync_active,value=buff.trueshot.up
# Uses buff effect trinkets with cooldowns and is willing to delay usage up to half the trinket cooldown if it won't lose a usage in the fight. Fills in downtime with weaker buff effects if they won't also be saved for later cooldowns (happens if it won't delay over half the trinket cooldown and a stronger trinket won't be up in time) or damage effects if they won't inferfere with any buff effect usage. Intended to be slot-agnostic so that any order of the same trinket pair should result in the same usage.
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket1,if=trinket.1.has_use_buff&(variable.buff_sync_ready&(variable.trinket_1_stronger|trinket.2.cooldown.remains)|!variable.buff_sync_ready&(variable.trinket_1_stronger&(variable.buff_sync_remains>trinket.1.cooldown.duration%3&fight_remains>trinket.1.cooldown.duration+20|trinket.2.has_use_buff&trinket.2.cooldown.remains>variable.buff_sync_remains-15&trinket.2.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains+45>fight_remains)|variable.trinket_2_stronger&(trinket.2.cooldown.remains&(trinket.2.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains>=20|trinket.2.cooldown.remains-5>=variable.buff_sync_remains&(variable.buff_sync_remains>trinket.1.cooldown.duration%3|trinket.1.cooldown.duration<fight_remains&(variable.buff_sync_remains+trinket.1.cooldown.duration>fight_remains)))|trinket.2.cooldown.ready&variable.buff_sync_remains>20&variable.buff_sync_remains<trinket.2.cooldown.duration%3)))|!trinket.1.has_use_buff&(trinket.1.cast_time=0|!variable.buff_sync_active)&(!trinket.2.has_use_buff&(variable.trinket_1_stronger|trinket.2.cooldown.remains)|trinket.2.has_use_buff&(variable.buff_sync_remains>20|trinket.2.cooldown.remains>20))|boss&fight_remains<25&(variable.trinket_1_stronger|trinket.2.cooldown.remains)
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket2,if=trinket.2.has_use_buff&(variable.buff_sync_ready&(variable.trinket_2_stronger|trinket.1.cooldown.remains)|!variable.buff_sync_ready&(variable.trinket_2_stronger&(variable.buff_sync_remains>trinket.2.cooldown.duration%3&fight_remains>trinket.2.cooldown.duration+20|trinket.1.has_use_buff&trinket.1.cooldown.remains>variable.buff_sync_remains-15&trinket.1.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains+45>fight_remains)|variable.trinket_1_stronger&(trinket.1.cooldown.remains&(trinket.1.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains>=20|trinket.1.cooldown.remains-5>=variable.buff_sync_remains&(variable.buff_sync_remains>trinket.2.cooldown.duration%3|trinket.2.cooldown.duration<fight_remains&(variable.buff_sync_remains+trinket.2.cooldown.duration>fight_remains)))|trinket.1.cooldown.ready&variable.buff_sync_remains>20&variable.buff_sync_remains<trinket.1.cooldown.duration%3)))|!trinket.2.has_use_buff&(trinket.2.cast_time=0|!variable.buff_sync_active)&(!trinket.1.has_use_buff&(variable.trinket_2_stronger|trinket.1.cooldown.remains)|trinket.1.has_use_buff&(variable.buff_sync_remains>20|trinket.1.cooldown.remains>20))|boss&fight_remains<25&(variable.trinket_2_stronger|trinket.1.cooldown.remains)

# ========== 版本说明 ==========

# 适用版本：魔兽世界11.2版本
# 完美1.1版本 - 基于SimulationCraft官方优化
# 优化日期：2025-08-16
# 主要特色：
# 1. 基于SimulationCraft官方APL的优化版本
# 2. 集成了手法文件的优化逻辑
# 3. 修复双目标循环中无效的多重射击使用（技巧射击需要3+目标才能触发）
# 4. 双目标循环改为基于单体循环的逻辑，使用奥术射击作为填充技能
# 5. 保留了原版的复杂饰品同步逻辑
# 6. 适用于追求稳定输出的玩家