# 系统自测步骤（小白版）

适用对象：首次上手自测的同事。按顺序做，边做边勾选。

---

## 0. 开始前
- [ ] 登录微加云表单
- [ ] 打开您的资产/维修应用
- [ ] 约定测试标记：统一用“【测试】”开头命名，便于后续删除
- [ ] 每一步都“截图留存”

---

## 1. 资产管理自测（约10分钟）

1. 打开“资产管理→资产列表”
   - [ ] 能看到列表和分页
2. 查看一条已有资产
   - [ ] 字段齐全（编码、名称、分类、状态、位置、图片等）
3. 新建一条“测试资产”（如：【测试】笔记本-001）
   - [ ] 只填必填项→保存
   - [ ] 成功出现在列表
4. 搜索与筛选
   - [ ] 用资产编码/名称搜索到“【测试】”
   - [ ] 用“状态/分类/部门/时间”任一筛选成功
5. 编辑与交接（任选其一验证）
   - [ ] 打开“【测试】”资产→修改状态或管理员→保存成功
   - [ ] 执行一次“交接/领用/盘点”操作→能保存记录
6. 清理
   - [ ] 删除“【测试】”资产或标记为“测试数据”

---

## 2. 维修管理自测（约15分钟）

1. 打开“维修管理→报修申请”
   - [ ] 表单能打开，默认时间自动填充
2. 提交一条“测试报修”（关联一件真实或测试资产）
   - [ ] 标题用“【测试】报修-001”
   - [ ] 填写：故障描述、紧急程度、图片（可随意）→提交成功
3. 审核/分配（按您系统现有流程）
   - [ ] 管理员能看到这条报修
   - [ ] 能分配到某个维修人员（或自己）
4. 维修处理
   - [ ] 维修人员能“接单”
   - [ ] 能填写处理过程/完成时间→提交
5. 验收与评价
   - [ ] 报修人能“验收通过”
   - [ ] 能做满意度评价→状态变为“已完成/已关闭”
6. 清理
   - [ ] 在标题或备注标注“测试”，保留或删除均可

---

## 3. 权限与通知（约10分钟）

1. 菜单可见性
   - [ ] 使用不同账号/角色登录（报修人/维修人员/管理员）
   - [ ] 各角色只能看到应有菜单
2. 操作权限
   - [ ] 普通用户：可提交报修，不能改他人数据
   - [ ] 维修人员：可处理分配给自己的工单
   - [ ] 管理员：可分配、查看统计
3. 通知验证（如系统支持）
   - [ ] 提交报修后，相关人员收到消息
   - [ ] 超时或流转有提醒（如已配置）

---

## 4. 统计与视图（约5分钟）

- [ ] 打开“统计/报表/仪表盘”
- [ ] 图表能显示数据（状态分布、趋势等）
- [ ] 能按时间或部门筛选

---

## 5. 记录问题（模板）

请把遇到的问题按以下格式记录并发我：

```
【问题】页面/步骤/按钮 + 现象（报错/卡顿/保存失败…）
【期望】希望达到什么效果
【截图】至少1张
【账号/角色】使用的账号类型
【时间】出现问题的大致时间
```

---

## 6. 完成后请回复我
- [ ] 已按步骤完成四个模块的勾选
- [ ] 把“问题清单+关键截图”打包发我

---

## 附：一页纸速查

资产自测顺序：打开→看一条→新增测试→搜索筛→编辑/交接→清理

维修自测顺序：提交测试→分配→接单→处理→验收→评价→清理

权限与通知：换角色看菜单→验证能做什么→看是否有消息提醒

统计与视图：能打开→有数据→可筛选

