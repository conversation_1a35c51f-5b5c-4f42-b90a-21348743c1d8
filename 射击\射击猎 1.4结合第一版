## Date: 2025-08-27
## 射击猎人手法 - 1.3单体450万优化版 v4.0

# ========== 预战斗设置 ==========
actions.precombat+=/summon_pet,if=talent.unbreakable_bond

# Determine the stronger trinket to sync with cooldowns. In descending priority: buff effects > damage effects, longer > shorter cooldowns, longer > shorter cast times. Special case to consider Mirror of Fractured Tomorrows weaker than other buff effects since its power is split between the dmg effect and the buff effect.
actions.precombat+=/variable,name=trinket_1_stronger,value=!trinket.2.has_cooldown|trinket.1.has_use_buff&(!trinket.2.has_use_buff|!trinket.1.is.mirror_of_fractured_tomorrows&(trinket.2.is.mirror_of_fractured_tomorrows|trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration))|!trinket.1.has_use_buff&(!trinket.2.has_use_buff&(trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration))
actions.precombat+=/variable,name=trinket_2_stronger,value=!variable.trinket_1_stronger

actions.precombat+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20
actions.precombat+=/aimed_shot,if=active_enemies<3|talent.black_arrow&talent.headshot
actions.precombat+=/steady_shot

# ========== 主循环 ==========
actions+=/counter_shot
actions+=/tranquilizing_shot

# Determine if it is a good time to use Trueshot. Raid event optimization takes priority so usage is saved for multiple targets as long as it won't delay over half its duration. Otherwise allow for small delays to line up buff effect trinkets, and when using Bullseye, delay the last usage of the fight for max stacks.
actions+=/variable,name=trueshot_ready,value=cooldown.trueshot.ready&(!talent.bullseye|fight_remains>cooldown.trueshot.duration_guess+buff.trueshot.duration%2|buff.bullseye.stack=buff.bullseye.max_stack)&(!trinket.1.has_use_buff|trinket.1.cooldown.remains>30|trinket.1.cooldown.ready)&(!trinket.2.has_use_buff|trinket.2.cooldown.remains>30|trinket.2.cooldown.ready)|boss&fight_remains<25
actions+=/mend_pet,if=pet.health_pct<pet_healing

actions+=/call_action_list,name=cds
actions+=/call_action_list,name=trinkets
actions+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20
actions+=/call_action_list,name=trickshots,strict=1,if=active_enemies>2&talent.trick_shots
actions+=/call_action_list,name=cleave,strict=1,if=active_enemies>1
actions+=/call_action_list,name=drst,strict=1,if=active_enemies=1&talent.black_arrow
actions+=/call_action_list,name=sentst,if=active_enemies=1&!talent.black_arrow

## actions.cds+=/invoke_external_buff,name=power_infusion,if=buff.trueshot.remains>12|fight_remains<13
actions.cds+=/berserking,if=buff.trueshot.up|boss&fight_remains<13
actions.cds+=/blood_fury,if=buff.trueshot.up|cooldown.trueshot.remains>30|boss&fight_remains<16
actions.cds+=/ancestral_call,if=buff.trueshot.up|cooldown.trueshot.remains>30|boss&fight_remains<16
actions.cds+=/fireblood,if=buff.trueshot.up|cooldown.trueshot.remains>30|boss&fight_remains<9
actions.cds+=/lights_judgment,if=buff.trueshot.down
actions.cds+=/potion,if=buff.trueshot.up&(buff.bloodlust.up|target.health.pct<20)|boss&fight_remains<31

# ========== 双目标循环 - 基于1.3版本优化 ==========
# 夺命射击最高优先级 - 斩杀阶段和BUFF期
actions.cleave+=/kill_shot,cycle_targets=1,if=talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)|!talent.headshot&buff.razor_fragments.up

# 黑蚀箭优先级 - 黑暗游侠天赋核心技能
actions.cleave+=/black_arrow,if=buff.precise_shots.up&buff.moving_target.down&variable.trueshot_ready
actions.cleave+=/black_arrow,if=talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)|!talent.headshot&buff.razor_fragments.up

# 百发百中开启
actions.cleave+=/trueshot,if=variable.trueshot_ready&buff.double_tap.down&focus>=variable.focus_threshold_high

# 瞄准射击优先级 - 基于1.3版本优化
actions.cleave+=/aimed_shot,cycle_targets=1,if=buff.lock_and_load.up&focus>=variable.aimed_shot_focus_cost
actions.cleave+=/aimed_shot,cycle_targets=1,if=variable.perfect_shot_window&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&focus>=variable.aimed_shot_focus_cost&buff.streamline.stack>=2
actions.cleave+=/aimed_shot,cycle_targets=1,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&focus>=variable.aimed_shot_focus_cost&buff.streamline.stack>=1

# 爆炸射击 - 获得荷枪实弹并刷新瞄准CD
actions.cleave+=/explosive_shot,if=action.aimed_shot.charges<1&cooldown.aimed_shot.remains>1

# 乱射优先级
actions.cleave+=/volley,if=buff.double_tap.down&(!raid_event.adds.exists|raid_event.adds.in>cooldown)

# 急速射击优化
actions.cleave+=/rapid_fire,if=talent.bulletstorm&buff.bulletstorm.down&!variable.burst_phase
actions.cleave+=/rapid_fire,if=talent.lunar_storm&buff.lunar_storm_cooldown.down&hero_tree.sentinel
actions.cleave+=/rapid_fire,if=!variable.burst_phase&(buff.bulletstorm.remains<3|!buff.bulletstorm.up)

# 填充技能 - 基于1.3版本优化
actions.cleave+=/multishot,cycle_targets=1,if=active_enemies>=2&buff.precise_shots.up&focus>=40
actions.cleave+=/arcane_shot,cycle_targets=1,if=buff.precise_shots.up&focus>=40
actions.cleave+=/multishot,if=active_enemies>=2&focus>=40

# 集中值管理 - 特殊情况下的稳固射击
actions.cleave+=/steady_shot,if=talent.black_arrow&focus+cast_regen<focus.max&prev.1.aimed_shot&!buff.deathblow.react&buff.trueshot.down&cooldown.trueshot.remains

# 最后选择稳固射击
actions.cleave+=/steady_shot

# ########## 1个目标 - 黑暗游侠天赋（基于默认参数优化）
actions.drst+=/explosive_shot,if=talent.precision_detonation&prev.1.aimed_shot&buff.trueshot.down&buff.lock_and_load.down
actions.drst+=/volley,if=buff.double_tap.down&(!raid_event.adds.exists|raid_event.adds.in>cooldown)
actions.drst+=/steady_shot,if=focus+cast_regen<focus.max&prev.1.aimed_shot&(!cooldown.black_arrow.ready)&buff.trueshot.down&cooldown.trueshot.remains
actions.drst+=/black_arrow,if=!talent.headshot|talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)
actions.drst+=/aimed_shot,if=buff.trueshot.up&buff.precise_shots.down|buff.lock_and_load.up&buff.moving_target.up
actions.drst+=/rapid_fire,if=!buff.deathblow.up
actions.drst+=/trueshot,if=variable.trueshot_ready&buff.double_tap.down&buff.deathblow.down
actions.drst+=/arcane_shot,if=buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)
actions.drst+=/aimed_shot,if=buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up
actions.drst+=/explosive_shot,if=talent.shrapnel_shot&buff.lock_and_load.down
actions.drst+=/steady_shot

# ########## 1个目标 - 哨兵天赋（基于默认参数优化）
actions.sentst+=/explosive_shot,if=talent.precision_detonation&prev.1.aimed_shot&buff.trueshot.down
actions.sentst+=/volley,if=buff.double_tap.down&(!raid_event.adds.exists|raid_event.adds.in>cooldown)
actions.sentst+=/trueshot,if=variable.trueshot_ready&buff.double_tap.down
actions.sentst+=/rapid_fire,if=talent.lunar_storm&buff.lunar_storm_cooldown.down
actions.sentst+=/kill_shot,if=talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)|!talent.headshot&buff.razor_fragments.up
actions.sentst+=/arcane_shot,if=buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)
actions.sentst+=/aimed_shot,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&full_recharge_time<action.rapid_fire.execute_time+cast_time&(!talent.bulletstorm|buff.bulletstorm.up)&talent.windrunner_quiver
actions.sentst+=/rapid_fire
actions.sentst+=/aimed_shot,if=buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up
actions.sentst+=/explosive_shot,if=talent.precision_detonation|buff.trueshot.down
actions.sentst+=/steady_shot

# ################ 3+ targets (with Trick Shots) - 基于1.3版本优化
actions.trickshots+=/explosive_shot,if=talent.precision_detonation&prev.1.aimed_shot&buff.trueshot.down&(!talent.shrapnel_shot|buff.lock_and_load.down)

# 乱射优化 - AOE爆发期使用（基于1.3版本优化）
actions.trickshots+=/volley,if=buff.double_tap.down&(!talent.shrapnel_shot|buff.lock_and_load.down)&(buff.trueshot.up|buff.lunar_storm.up)
actions.trickshots+=/volley,if=buff.double_tap.down&(!talent.shrapnel_shot|buff.lock_and_load.down)&!buff.trueshot.up&!buff.lunar_storm.up&cooldown.trueshot.remains>10

# 急速射击优化
actions.trickshots+=/rapid_fire,if=talent.bulletstorm&buff.bulletstorm.down&buff.trick_shots.remains>execute_time
actions.trickshots+=/rapid_fire,if=hero_tree.sentinel&buff.lunar_storm_cooldown.down&buff.trick_shots.remains>execute_time

# 瞄准射击后接稳固 - 避免误按奥术射击导致黑箭空转
actions.trickshots+=/steady_shot,if=talent.black_arrow&focus+cast_regen<focus.max&prev.1.aimed_shot&!buff.deathblow.react&buff.trueshot.down&cooldown.trueshot.remains

# 黑蚀箭使用 - 基于1.3版本优化
actions.trickshots+=/black_arrow,if=!talent.headshot|buff.precise_shots.up|buff.trick_shots.down

# 多重射击技巧射击优化 - 确保技巧射击BUFF不断（基于1.3版本优化）
actions.trickshots+=/multishot,cycle_targets=1,if=buff.trick_shots.down|buff.trick_shots.remains<gcd.max

# 百发百中开启
actions.trickshots+=/trueshot,if=variable.trueshot_ready&buff.trick_shots.up&buff.double_tap.down&focus>=variable.focus_threshold_high

# 瞄准射击优先级 - 基于1.3版本优化
actions.trickshots+=/aimed_shot,if=buff.trick_shots.up&action.aimed_shot.charges>=1&focus>=variable.aimed_shot_focus_cost&(buff.streamline.stack>=1|buff.trueshot.up)

# 多重射击维护技巧射击BUFF
actions.trickshots+=/multishot,cycle_targets=1,if=buff.precise_shots.up&buff.trick_shots.up&focus>=40
actions.trickshots+=/multishot,if=buff.trick_shots.up&active_enemies>=3&focus>=40

# 集中值管理 - 基于1.3版本优化
actions.trickshots+=/multishot,if=focus>=40&active_enemies>=3
actions.trickshots+=/arcane_shot,if=focus>=40&buff.precise_shots.up

# 最后选择稳固射击
actions.trickshots+=/steady_shot,if=focus<30&!variable.burst_phase&buff.trick_shots.up

actions.trinkets+=/variable,name=sync_ready,value=cooldown.trueshot.ready
actions.trinkets+=/variable,name=sync_remains,value=cooldown.trueshot.remains
actions.trinkets+=/variable,name=sync_active,value=buff.trueshot.up
actions.trinkets+=/variable,name=damage_sync_active,value=buff.trueshot.up
actions.trinkets+=/variable,name=damage_sync_remains,value=cooldown.trueshot.remains
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket1,if=trinket.1.has_use_buff&(variable.sync_ready&(variable.trinket_1_stronger|trinket.2.cooldown.remains)|!variable.sync_ready&(variable.trinket_1_stronger&(variable.sync_remains>trinket.1.cooldown.duration%3&fight_remains>trinket.1.cooldown.duration+20|trinket.2.has_use_buff&trinket.2.cooldown.remains>variable.sync_remains-15&trinket.2.cooldown.remains-5<variable.sync_remains&variable.sync_remains+45>fight_remains)|variable.trinket_2_stronger&(trinket.2.cooldown.remains&(trinket.2.cooldown.remains-5<variable.sync_remains&variable.sync_remains>=20|trinket.2.cooldown.remains-5>=variable.sync_remains&(variable.sync_remains>trinket.1.cooldown.duration%3|trinket.1.cooldown.duration<fight_remains&(variable.sync_remains+trinket.1.cooldown.duration>fight_remains)))|trinket.2.cooldown.ready&variable.sync_remains>20&variable.sync_remains<trinket.2.cooldown.duration%3)))|!trinket.1.has_use_buff&(trinket.1.cast_time=0|!variable.sync_active)&(!trinket.2.has_use_buff&(variable.trinket_1_stronger|trinket.2.cooldown.remains)|trinket.2.has_use_buff&(variable.sync_remains>20|trinket.2.cooldown.remains>20))|boss&fight_remains<25&(variable.trinket_1_stronger|trinket.2.cooldown.remains)
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket2,if=trinket.2.has_use_buff&(variable.sync_ready&(variable.trinket_2_stronger|trinket.1.cooldown.remains)|!variable.sync_ready&(variable.trinket_2_stronger&(variable.sync_remains>trinket.2.cooldown.duration%3&fight_remains>trinket.2.cooldown.duration+20|trinket.1.has_use_buff&trinket.1.cooldown.remains>variable.sync_remains-15&trinket.1.cooldown.remains-5<variable.sync_remains&variable.sync_remains+45>fight_remains)|variable.trinket_1_stronger&(trinket.1.cooldown.remains&(trinket.1.cooldown.remains-5<variable.sync_remains&variable.sync_remains>=20|trinket.1.cooldown.remains-5>=variable.sync_remains&(variable.sync_remains>trinket.2.cooldown.duration%3|trinket.2.cooldown.duration<fight_remains&(variable.sync_remains+trinket.2.cooldown.duration>fight_remains)))|trinket.1.cooldown.ready&variable.sync_remains>20&variable.sync_remains<trinket.1.cooldown.duration%3)))|!trinket.2.has_use_buff&(trinket.2.cast_time=0|!variable.sync_active)&(!trinket.1.has_use_buff&(variable.trinket_2_stronger|trinket.1.cooldown.remains)|trinket.1.has_use_buff&(variable.sync_remains>20|trinket.1.cooldown.remains>20))|boss&fight_remains<25&(variable.trinket_2_stronger|trinket.1.cooldown.remains)