
## Date: 2025-08-16-23:05:05

actions.precombat+=/summon_pet,if=talent.unbreakable_bond
# Determine the stronger trinket to sync with cooldowns. In descending priority: buff effects > damage effects, longer > shorter cooldowns, longer > shorter cast times. Special case to consider Mirror of Fractured Tomorrows weaker than other buff effects since its power is split between the dmg effect and the buff effect.
actions.precombat+=/variable,name=trinket_1_stronger,value=!trinket.2.has_cooldown|trinket.1.has_use_buff&(!trinket.2.has_use_buff|!trinket.1.is.mirror_of_fractured_tomorrows&(trinket.2.is.mirror_of_fractured_tomorrows|trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration))|!trinket.1.has_use_buff&(!trinket.2.has_use_buff&(trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration))
actions.precombat+=/variable,name=trinket_2_stronger,value=!variable.trinket_1_stronger
actions.precombat+=/variable,name=pet_healing,value=40

# 射击猎人核心变量系统 - 基于手法文件优化
actions.precombat+=/variable,name=focus_regen_multiplier,value=1.0+stat.haste_rating%32500
actions.precombat+=/variable,name=aimed_shot_focus_cost,value=35*(1-0.5*buff.trueshot.up)
actions.precombat+=/variable,name=focus_threshold_high,value=60
actions.precombat+=/variable,name=focus_threshold_low,value=35

# 射击猎人阶段管理变量 - 基于手法文件的三阶段理论
actions.precombat+=/variable,name=perfect_shot_window,value=buff.trueshot.up&buff.bulletstorm.up&(buff.lunar_storm.up|!hero_tree.sentinel)

# 起手式准备 - 基于手法文件的标准起手（完全手动起手，无自动技能）
actions.precombat+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20
# 起手式第1步：开怪前三秒预读瞄准射击 - 完全手动执行
# 移除所有precombat技能，确保完全手动起手
# actions.precombat+=/aimed_shot,if=active_enemies<3|talent.black_arrow&talent.headshot
# actions.precombat+=/steady_shot

actions+=/counter_shot
actions+=/tranquilizing_shot

# 射击猎人阶段管理变量 - 动态更新
actions+=/variable,name=trueshot_phase,value=buff.trueshot.up
actions+=/variable,name=stable_phase,value=!buff.trueshot.up&cooldown.trueshot.remains>15
actions+=/variable,name=burst_phase,value=buff.trueshot.up|(buff.lunar_storm.up&hero_tree.sentinel)

# Determine if it is a good time to use Trueshot. Raid event optimization takes priority so usage is saved for multiple targets as long as it won't delay over half its duration. Otherwise allow for small delays to line up buff effect trinkets, and when using Bullseye, delay the last usage of the fight for max stacks.
actions+=/variable,name=trueshot_ready,value=cooldown.trueshot.ready&(!talent.bullseye|fight_remains>cooldown.trueshot.duration_guess+buff.trueshot.duration%2|buff.bullseye.stack=buff.bullseye.max_stack)&(!trinket.1.has_use_buff|trinket.1.cooldown.remains>30|trinket.1.cooldown.ready)&(!trinket.2.has_use_buff|trinket.2.cooldown.remains>30|trinket.2.cooldown.ready)|boss&fight_remains<25
actions+=/mend_pet,if=pet.health_pct<pet_healing

# 射击猎人起手式 - 基于手法文件的标准7步起手
actions+=/call_action_list,name=opener,strict=1,if=time<15&!variable.opener_complete

actions+=/call_action_list,name=cds
actions+=/call_action_list,name=trinkets
actions+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20
actions+=/call_action_list,name=trickshots,strict=1,if=active_enemies>2&talent.trick_shots
actions+=/call_action_list,name=cleave,strict=1,if=active_enemies>1
actions+=/call_action_list,name=st

# ################# 射击猎人起手式 - 基于手法文件的标准7步起手
# 起手式变量管理
actions.opener+=/variable,name=opener_step,value=2,if=!variable.opener_step
actions.opener+=/variable,name=opener_complete,value=1,if=variable.opener_step>=8

# 起手式第2步：黑箭，消耗弹无虚发，获得2层行云流水（第1步瞄准射击完全手动执行）
actions.opener+=/black_arrow,if=variable.opener_step=2&(buff.precise_shots.up|time>3)
actions.opener+=/variable,name=opener_step,value=3,if=variable.opener_step=2&prev.1.black_arrow

# 起手式第3步：急速射击，叠加子弹风暴层数
actions.opener+=/rapid_fire,if=variable.opener_step=3
actions.opener+=/variable,name=opener_step,value=4,if=variable.opener_step=3&prev.1.rapid_fire

# 起手式第4步：瞄准射击，获得2层弹无虚发，消耗2层行云流水
actions.opener+=/aimed_shot,if=variable.opener_step=4&buff.streamline.stack>=2
actions.opener+=/variable,name=opener_step,value=5,if=variable.opener_step=4&prev.1.aimed_shot

# 起手式第5步：爆炸射击，获得荷枪实弹
actions.opener+=/explosive_shot,if=variable.opener_step=5
actions.opener+=/variable,name=opener_step,value=6,if=variable.opener_step=5&prev.1.explosive_shot

# 起手式第6步：百发百中，开启爆发期
actions.opener+=/trueshot,if=variable.opener_step=6&variable.trueshot_ready
actions.opener+=/variable,name=opener_step,value=6.5,if=variable.opener_step=6&prev.1.trueshot

# 起手式第6.5步：黑箭，消耗2层弹无虚发以及碎刀片和夺命打击
actions.opener+=/black_arrow,if=variable.opener_step=6.5&buff.trueshot.up&buff.precise_shots.stack>=2
actions.opener+=/variable,name=opener_step,value=7,if=variable.opener_step=6.5&prev.1.black_arrow

# 起手式第7步：瞄准射击，消耗荷枪实弹，获得2层弹无虚发
actions.opener+=/aimed_shot,if=variable.opener_step=7&buff.lock_and_load.up
actions.opener+=/variable,name=opener_step,value=8,if=variable.opener_step=7&prev.1.aimed_shot

# 起手式容错处理 - 如果某步骤条件不满足，跳过到下一步
actions.opener+=/variable,name=opener_step,value=variable.opener_step+1,if=time>variable.opener_step*2&variable.opener_step<8

# 起手式备用逻辑 - 如果条件不满足，执行基础技能
actions.opener+=/black_arrow,if=variable.opener_step<=3&buff.precise_shots.up&!prev.1.black_arrow
actions.opener+=/rapid_fire,if=variable.opener_step<=4&!prev.1.rapid_fire&!prev.2.rapid_fire
actions.opener+=/aimed_shot,if=variable.opener_step<=5&buff.streamline.stack>=1&focus>=35
actions.opener+=/explosive_shot,if=variable.opener_step<=6
actions.opener+=/trueshot,if=variable.opener_step<=7&variable.trueshot_ready
actions.opener+=/black_arrow,if=variable.opener_step<=7&buff.trueshot.up&buff.precise_shots.stack>=2&!prev.1.black_arrow
actions.opener+=/steady_shot,if=focus<50

## actions.cds+=/invoke_external_buff,name=power_infusion,if=buff.trueshot.remains>12|fight_remains<13
actions.cds+=/berserking,if=buff.trueshot.up|boss&fight_remains<13
actions.cds+=/blood_fury,if=buff.trueshot.up|cooldown.trueshot.remains>30|boss&fight_remains<16
actions.cds+=/ancestral_call,if=buff.trueshot.up|cooldown.trueshot.remains>30|boss&fight_remains<16
actions.cds+=/fireblood,if=buff.trueshot.up|cooldown.trueshot.remains>30|boss&fight_remains<9
actions.cds+=/lights_judgment,if=buff.trueshot.down
# 药水使用优化 - 与多BUFF同步（基于手法文件优化）
actions.cds+=/potion,if=variable.perfect_shot_window|(buff.trueshot.up&(buff.bloodlust.up|target.health.pct<20))|boss&fight_remains<31

# ################# 2 targets (2+ without Trick Shots) - 优化版（修复无效多重射击）
# 夺命射击最高优先级 - 斩杀阶段和BUFF期
actions.cleave+=/kill_shot,cycle_targets=1,if=talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)|!talent.headshot&buff.razor_fragments.up

# 黑蚀箭优先级 - 黑暗游侠天赋核心技能
actions.cleave+=/black_arrow,if=buff.precise_shots.up&buff.moving_target.down&variable.trueshot_ready
actions.cleave+=/black_arrow,if=talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)|!talent.headshot&buff.razor_fragments.up

# 百发百中开启 - 冷却好就用
actions.cleave+=/trueshot,if=variable.trueshot_ready&(buff.double_tap.down|!talent.volley)&(buff.lunar_storm_cooldown.up|!talent.double_tap|!talent.volley)&(buff.precise_shots.down|buff.moving_target.up|!talent.volley)

# 哨兵天赋月之风暴优化
actions.cleave+=/rapid_fire,if=talent.lunar_storm&buff.lunar_storm_cooldown.down&(buff.precise_shots.down|buff.moving_target.up|cooldown.volley.remains&cooldown.trueshot.remains|!talent.volley)
actions.cleave+=/aimed_shot,cycle_targets=1,if=hero_tree.sentinel&buff.lunar_storm.up&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&(buff.streamline.stack>=1|focus>=50)

# 爆炸射击 - 精确引爆连招
actions.cleave+=/explosive_shot,if=talent.precision_detonation&prev.1.aimed_shot&(buff.trueshot.down|!talent.windrunner_quiver)

# 乱射优先级 - 双目标有效AOE技能
actions.cleave+=/volley,if=(talent.double_tap&buff.double_tap.down|!talent.aspect_of_the_hydra)&(buff.precise_shots.down|buff.moving_target.up)
actions.cleave+=/volley,if=!talent.double_tap&(buff.precise_shots.down|buff.moving_target.up)

# 瞄准射击优先级 - 主要输出技能
actions.cleave+=/aimed_shot,cycle_targets=1,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&full_recharge_time<action.rapid_fire.execute_time+cast_time&(!talent.bulletstorm|buff.bulletstorm.up)&talent.windrunner_quiver
actions.cleave+=/aimed_shot,cycle_targets=1,if=buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up

# 急速射击优化 - BUFF维护
actions.cleave+=/rapid_fire,if=talent.bulletstorm&buff.bulletstorm.down&(!talent.double_tap|buff.double_tap.up|!talent.aspect_of_the_hydra&buff.trick_shots.remains>execute_time)&(buff.precise_shots.down|buff.moving_target.up|!talent.volley)
actions.cleave+=/rapid_fire,if=!talent.bulletstorm|buff.bulletstorm.stack<=10|talent.aspect_of_the_hydra
actions.cleave+=/rapid_fire

# 填充技能优化 - 移除无效多重射击，优先奥术射击
actions.cleave+=/arcane_shot,cycle_targets=1,if=buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)

# 爆炸射击常规使用
actions.cleave+=/explosive_shot,if=talent.precision_detonation|buff.trueshot.down

# 黑蚀箭常规使用
actions.cleave+=/black_arrow,if=!talent.headshot

# 集中值管理 - 特殊情况下的稳固射击
actions.cleave+=/steady_shot,if=talent.black_arrow&focus+cast_regen<focus.max&prev.1.aimed_shot&!buff.deathblow.react&buff.trueshot.down&cooldown.trueshot.remains

# 最后选择稳固射击
actions.cleave+=/steady_shot

# ########## 1个目标 - 基于手法文件的三阶段优化循环

# 百发百中期间的优化循环 - 基于手法文件要点
actions.st+=/call_action_list,name=trueshot_phase,strict=1,if=variable.trueshot_phase

# 平稳期的优化循环 - 基于手法文件要点
actions.st+=/call_action_list,name=stable_phase,strict=1,if=variable.stable_phase

# 通用优先级处理
actions.st+=/call_action_list,name=general_priority

# ################# 百发百中期 - 基于手法文件要点优化
# 要点一：奥术射击尽可能少释放，仅仅作为获取行云流水buff的工具
# 要点二：黑箭每一发黑箭必须吃到2层弹无虚发效果。黑箭尽量不要连打，除非是百发最后一秒触发
# 要点三：除黑箭外，急速射击优先级排第二，施放急速射击有极大概率触发夺命打击
# 要点四：荷枪瞄准不消耗行云流水，在荷枪瞄准后，可以直接拉瞄准

# 黑箭最高优先级 - 必须吃到2层弹无虚发效果
actions.trueshot_phase+=/black_arrow,if=buff.precise_shots.stack>=2&(!debuff.black_arrow.up|debuff.black_arrow.remains<3)

# 急速射击第二优先级 - 触发夺命打击概率高
actions.trueshot_phase+=/rapid_fire,if=!prev.1.black_arrow&(buff.bulletstorm.down|buff.bulletstorm.stack<15)

# 荷枪实弹瞄准射击 - 不消耗行云流水
actions.trueshot_phase+=/aimed_shot,if=buff.lock_and_load.up&focus>=variable.aimed_shot_focus_cost

# 完美瞄准射击窗口 - 多BUFF叠加时优先
actions.trueshot_phase+=/aimed_shot,if=variable.perfect_shot_window&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&focus>=variable.aimed_shot_focus_cost&buff.streamline.stack>=2

# 百发百中期间常规瞄准射击
actions.trueshot_phase+=/aimed_shot,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&focus>=variable.aimed_shot_focus_cost&buff.streamline.stack>=1

# 爆炸射击 - 获得荷枪实弹并刷新瞄准CD
actions.trueshot_phase+=/explosive_shot,if=action.aimed_shot.charges<1&cooldown.aimed_shot.remains>1

# 奥术射击 - 仅作为获取行云流水buff的工具，最低优先级
actions.trueshot_phase+=/arcane_shot,if=buff.streamline.down&focus>50

# 稳固射击 - 集中值不足时使用
actions.trueshot_phase+=/steady_shot,if=focus<variable.aimed_shot_focus_cost

# ################# 平稳期 - 基于手法文件要点优化
# 要点一：拉完瞄准后接稳固，避免瞄准出触发夺命打击后，误按奥术射击导致黑箭出现空转
# 要点二：急速射击优先级仅次于黑箭
# 要点三：爆炸射击可以给予荷枪实弹并使瞄准刷新一层cd，使用爆炸射击避免瞄准射击溢出的同时，爆炸射击也尽量卡cd使用
# 要点四：进入爆发期前，请保持60以上的集中值

# 黑箭最高优先级
actions.stable_phase+=/black_arrow,if=buff.precise_shots.stack>=2

# 急速射击第二优先级
actions.stable_phase+=/rapid_fire,if=(buff.bulletstorm.down|buff.bulletstorm.stack<10)&focus>=40

# 瞄准射击后接稳固 - 避免误按奥术射击
actions.stable_phase+=/steady_shot,if=prev.1.aimed_shot&!buff.deathblow.react

# 爆炸射击 - 卡CD使用，避免瞄准溢出
actions.stable_phase+=/explosive_shot,if=cooldown.explosive_shot.ready&(action.aimed_shot.charges>=1.5|cooldown.aimed_shot.remains<2)

# 瞄准射击 - 有行云流水BUFF时使用
actions.stable_phase+=/aimed_shot,if=buff.streamline.stack>=1&focus>=variable.aimed_shot_focus_cost

# 集中值管理 - 保持60以上集中值准备爆发期
actions.stable_phase+=/steady_shot,if=focus<variable.focus_threshold_high&cooldown.trueshot.remains<10

# 奥术射击 - 消耗弹无虚发获取行云流水
actions.stable_phase+=/arcane_shot,if=buff.precise_shots.up&buff.streamline.down

# 稳固射击 - 填充技能
actions.stable_phase+=/steady_shot

# ################# 通用优先级处理
actions.general_priority+=/kill_shot,if=(talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)|!talent.headshot&buff.razor_fragments.up)|(hero_tree.sentinel&buff.precise_shots.up&target.health.pct>20)
actions.general_priority+=/black_arrow,if=buff.precise_shots.stack>=2&hero_tree.dark_ranger
actions.general_priority+=/explosive_shot,if=talent.precision_detonation&prev.1.aimed_shot&buff.trueshot.down
actions.general_priority+=/volley,if=buff.double_tap.down&(!raid_event.adds.exists|raid_event.adds.in>cooldown)
actions.general_priority+=/trueshot,if=variable.trueshot_ready&buff.double_tap.down&focus>=variable.focus_threshold_high
actions.general_priority+=/aimed_shot,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&(buff.streamline.stack>=1|buff.trueshot.up)
actions.general_priority+=/rapid_fire,if=(!buff.trueshot.up|focus<50)&(buff.bulletstorm.remains<3|!buff.bulletstorm.up)&active_enemies<3
actions.general_priority+=/arcane_shot,if=buff.precise_shots.up
actions.general_priority+=/steady_shot

# ################ 3+ targets (with Trick Shots)
actions.trickshots+=/explosive_shot,if=talent.precision_detonation&prev.1.aimed_shot&buff.trueshot.down&(!talent.shrapnel_shot|buff.lock_and_load.down)
# 乱射优化 - AOE爆发期使用（基于手法文件优化）
actions.trickshots+=/volley,if=buff.double_tap.down&(!talent.shrapnel_shot|buff.lock_and_load.down)&(buff.trueshot.up|buff.lunar_storm.up)
actions.trickshots+=/volley,if=buff.double_tap.down&(!talent.shrapnel_shot|buff.lock_and_load.down)&!buff.trueshot.up&!buff.lunar_storm.up&cooldown.trueshot.remains>10
actions.trickshots+=/rapid_fire,if=talent.bulletstorm&buff.bulletstorm.down&buff.trick_shots.remains>execute_time
actions.trickshots+=/rapid_fire,if=hero_tree.sentinel&buff.lunar_storm_cooldown.down&buff.trick_shots.remains>execute_time
# Queue Steady Shot after Aimed Shot if a Deathblow hasn't already been up long enough to be reacted to.
actions.trickshots+=/steady_shot,if=talent.black_arrow&focus+cast_regen<focus.max&prev.1.aimed_shot&!buff.deathblow.react&buff.trueshot.down&cooldown.trueshot.remains
actions.trickshots+=/black_arrow,if=!talent.headshot|buff.precise_shots.up|buff.trick_shots.down
# Retarget to possibly spread an extra Spotter's Mark if able.
# 多重射击技巧射击优化 - 确保技巧射击BUFF不断（基于手法文件优化）
actions.trickshots+=/multishot,cycle_targets=1,if=buff.trick_shots.down|buff.trick_shots.remains<gcd.max
actions.trickshots+=/multishot,cycle_targets=1,if=buff.precise_shots.up&buff.moving_target.down&buff.trick_shots.up
actions.trickshots+=/trueshot,if=variable.trueshot_ready&buff.double_tap.down
actions.trickshots+=/volley,if=buff.double_tap.down&(!talent.salvo|!talent.precision_detonation|(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up))
# Prioritize Aimed Shot a little higher when close to capping charges.
actions.trickshots+=/aimed_shot,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&buff.trick_shots.up&buff.bulletstorm.up&full_recharge_time<gcd
actions.trickshots+=/rapid_fire,if=buff.trick_shots.remains>execute_time&(!talent.black_arrow|buff.deathblow.down)&(!talent.no_scope|debuff.spotters_mark.down)&(talent.no_scope|buff.bulletstorm.down)
actions.trickshots+=/explosive_shot,if=talent.precision_detonation&talent.shrapnel_shot&buff.lock_and_load.down&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)
actions.trickshots+=/aimed_shot,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&buff.trick_shots.up
actions.trickshots+=/explosive_shot,if=!talent.shrapnel_shot
actions.trickshots+=/steady_shot,if=focus+cast_regen<focus.max
actions.trickshots+=/multishot

actions.trinkets+=/variable,name=buff_sync_ready,value=cooldown.trueshot.ready
actions.trinkets+=/variable,name=buff_sync_remains,value=cooldown.trueshot.remains
actions.trinkets+=/variable,name=buff_sync_active,value=buff.trueshot.up
# Uses buff effect trinkets with cooldowns and is willing to delay usage up to half the trinket cooldown if it won't lose a usage in the fight. Fills in downtime with weaker buff effects if they won't also be saved for later cooldowns (happens if it won't delay over half the trinket cooldown and a stronger trinket won't be up in time) or damage effects if they won't inferfere with any buff effect usage. Intended to be slot-agnostic so that any order of the same trinket pair should result in the same usage.
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket1,if=trinket.1.has_use_buff&(variable.buff_sync_ready&(variable.trinket_1_stronger|trinket.2.cooldown.remains)|!variable.buff_sync_ready&(variable.trinket_1_stronger&(variable.buff_sync_remains>trinket.1.cooldown.duration%3&fight_remains>trinket.1.cooldown.duration+20|trinket.2.has_use_buff&trinket.2.cooldown.remains>variable.buff_sync_remains-15&trinket.2.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains+45>fight_remains)|variable.trinket_2_stronger&(trinket.2.cooldown.remains&(trinket.2.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains>=20|trinket.2.cooldown.remains-5>=variable.buff_sync_remains&(variable.buff_sync_remains>trinket.1.cooldown.duration%3|trinket.1.cooldown.duration<fight_remains&(variable.buff_sync_remains+trinket.1.cooldown.duration>fight_remains)))|trinket.2.cooldown.ready&variable.buff_sync_remains>20&variable.buff_sync_remains<trinket.2.cooldown.duration%3)))|!trinket.1.has_use_buff&(trinket.1.cast_time=0|!variable.buff_sync_active)&(!trinket.2.has_use_buff&(variable.trinket_1_stronger|trinket.2.cooldown.remains)|trinket.2.has_use_buff&(variable.buff_sync_remains>20|trinket.2.cooldown.remains>20))|boss&fight_remains<25&(variable.trinket_1_stronger|trinket.2.cooldown.remains)
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket2,if=trinket.2.has_use_buff&(variable.buff_sync_ready&(variable.trinket_2_stronger|trinket.1.cooldown.remains)|!variable.buff_sync_ready&(variable.trinket_2_stronger&(variable.buff_sync_remains>trinket.2.cooldown.duration%3&fight_remains>trinket.2.cooldown.duration+20|trinket.1.has_use_buff&trinket.1.cooldown.remains>variable.buff_sync_remains-15&trinket.1.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains+45>fight_remains)|variable.trinket_1_stronger&(trinket.1.cooldown.remains&(trinket.1.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains>=20|trinket.1.cooldown.remains-5>=variable.buff_sync_remains&(variable.buff_sync_remains>trinket.2.cooldown.duration%3|trinket.2.cooldown.duration<fight_remains&(variable.buff_sync_remains+trinket.2.cooldown.duration>fight_remains)))|trinket.1.cooldown.ready&variable.buff_sync_remains>20&variable.buff_sync_remains<trinket.1.cooldown.duration%3)))|!trinket.2.has_use_buff&(trinket.2.cast_time=0|!variable.buff_sync_active)&(!trinket.1.has_use_buff&(variable.trinket_2_stronger|trinket.1.cooldown.remains)|trinket.1.has_use_buff&(variable.buff_sync_remains>20|trinket.1.cooldown.remains>20))|boss&fight_remains<25&(variable.trinket_2_stronger|trinket.1.cooldown.remains)
