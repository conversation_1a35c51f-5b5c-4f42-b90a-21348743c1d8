#优化日期：2025-08-27 1:08 - 狂野怒火期间优化版本
hunter="兽王魔兽世界优化v2.3"
source=default
spec=beast_mastery
level=80
race=human
role=attack
position=ranged_back
professions=leatherworking=69/engineering=27
talents=C0PA11Mk8aZ38kAf+zso3nZ9IAMmxwCZDmRDNsBAAAAAgxMm5BGzMDmZGsMjZmxYmZmMjZMzMzgZGGGjZmhZbYYmhNAAAAAAYGA

# 消耗品配置 - 仅保留实际使用的消耗品
potion=tempered_potion_3
food=the_sushi_special

# 战前准备 
actions.precombat=summon_pet
actions.precombat+=/snapshot_stats
# 饰品强度判断变量 
actions.precombat+=/variable,name=stronger_trinket_slot,op=setif,value=1,value_else=2,condition=!trinket.2.has_cooldown|trinket.1.has_use_buff&(!trinket.2.has_use_buff|trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration)|!trinket.1.has_use_buff&(!trinket.2.has_use_buff&(trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration))

# 猎群领袖爆发期状态变量
actions.precombat+=/variable,name=pack_leader_burst,value=0
actions.precombat+=/variable,name=trinket_window,value=0
actions.precombat+=/variable,name=cotw_window,value=0
actions.precombat+=/variable,name=burst_incoming,value=0

# 兽王核心变量系统 
actions.precombat+=/variable,name=bear_priority,value=buff.howl_of_the_pack_leader_bear.up|cooldown.pack_leader.remains<8
actions.precombat+=/variable,name=perfect_storm,value=buff.howl_of_the_pack_leader_bear.up&buff.bestial_wrath.up
actions.precombat+=/variable,name=focus_threshold_high,value=75
actions.precombat+=/variable,name=focus_threshold_low,value=40
actions.precombat+=/variable,name=burst_window_active,value=buff.bestial_wrath.up|cooldown.bestial_wrath.remains<3

# 起手爆发优化 - 倒刺→杀戮→倒刺→爆发技能
actions.precombat+=/barbed_shot,if=!buff.hunter_master.up|buff.hunter_master.stack<5
actions.precombat+=/kill_command,if=buff.hunter_master.stack>=4&buff.hunter_master.stack<5
actions.precombat+=/barbed_shot,if=buff.hunter_master.stack>=4&action.barbed_shot.charges>=1
actions.precombat+=/bestial_wrath

# 主循环
actions=auto_shot
# 反制技能优先级 
actions+=/counter_shot
actions+=/tranquilizing_shot
# 保命技能优先级 - 血线管理
actions+=/primal_instincts,if=health.pct<=60
actions+=/exhilaration,if=health.pct<=50
actions+=/aspect_of_the_turtle,if=health.pct<=15
# 修复后的治疗宠物逻辑
actions+=/mend_pet,if=pet.health_pct<pet_healing
# 猎人印记管理
actions+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20

# 动态变量实时更新 
actions+=/variable,name=perfect_storm,value=buff.howl_of_the_pack_leader_bear.up&buff.bestial_wrath.up
actions+=/variable,name=burst_window_active,value=buff.bestial_wrath.up|cooldown.bestial_wrath.remains<3
actions+=/variable,name=focus_starved,value=focus<variable.focus_threshold_low&focus.regen<15
# 猎群领袖爆发期管理 
actions+=/variable,name=pack_leader_burst,value=buff.call_of_the_wild.up|buff.bestial_wrath.up|buff.bloodshed.up|buff.lead_from_the_front.up|buff.hunters_prey.up|cooldown.call_of_the_wild.remains<3|cooldown.bestial_wrath.remains<3|cooldown.bloodshed.remains<3
actions+=/variable,name=burst_incoming,value=cooldown.call_of_the_wild.remains<5|cooldown.bestial_wrath.remains<5|cooldown.bloodshed.remains<5

actions+=/call_action_list,name=cds
actions+=/call_action_list,name=trinkets
# 参考官方推导的目标数量判断逻辑
actions+=/call_action_list,name=drst,if=talent.black_arrow&(active_enemies<2|!talent.beast_cleave&active_enemies<3)
actions+=/call_action_list,name=drcleave,if=talent.black_arrow&(active_enemies>2|talent.beast_cleave&active_enemies>1)
actions+=/call_action_list,name=st,if=!talent.black_arrow&(active_enemies<2|!talent.beast_cleave&active_enemies<3)
actions+=/call_action_list,name=cleave,if=!talent.black_arrow&(active_enemies>2|talent.beast_cleave&active_enemies>1)

# CD技能管理 
actions.cds=invoke_external_buff,name=power_infusion,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&(buff.bestial_wrath.up|cooldown.bestial_wrath.remains<30)|fight_remains<16
# 种族技能 
actions.cds+=/berserking,if=variable.pack_leader_burst|buff.call_of_the_wild.up|buff.bestial_wrath.up|fight_remains<13
actions.cds+=/blood_fury,if=variable.pack_leader_burst|buff.call_of_the_wild.up|buff.bestial_wrath.up|fight_remains<16
actions.cds+=/ancestral_call,if=variable.pack_leader_burst|buff.call_of_the_wild.up|buff.bestial_wrath.up|fight_remains<16
actions.cds+=/fireblood,if=variable.pack_leader_burst|buff.call_of_the_wild.up|buff.bestial_wrath.up|fight_remains<9   

# 单体循环（无黑蚀箭天赋）- 核心机制：杀戮命令+倒刺射击双核心输出
# 夺命射击 - 斩杀技能，最高优先级
actions.st=kill_shot

# 完美风暴状态（猎群领袖之嚎特殊组合）- 杀戮命令输出极高期间
actions.st+=/kill_command,if=variable.perfect_storm&action.kill_command.charges>=1
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=variable.perfect_storm&action.barbed_shot.charges>=1&debuff.barbed_shot.stack<1

# 爆发技能 - CD好了就用，不等待
actions.st+=/bestial_wrath
actions.st+=/call_of_the_wild
actions.st+=/bloodshed

# 狂野怒火期间优化循环 - 集中值充足时优先杀戮命令
# 倒刺射击 - 防止溢出充能（狂野怒火期间也要防溢出）
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=action.barbed_shot.charges>=2
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=full_recharge_time<gcd

# 狂野怒火期间：集中值充足时，倒刺1层充能也要优先杀戮命令
actions.st+=/kill_command,if=buff.bestial_wrath.up&focus>=50&charges>=1
# 狂野怒火期间：杀戮命令不够时用眼镜蛇重置
actions.st+=/cobra_shot,if=buff.bestial_wrath.up&focus>=50&action.kill_command.charges<1&action.barbed_shot.charges>=1
# 狂野怒火期间：集中值不够时才打倒刺回复集中值
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=buff.bestial_wrath.up&focus<50&charges>=1

# 杀戮命令 - 熊在场时优先使用
actions.st+=/kill_command,if=buff.howl_of_the_pack_leader_bear.up&charges>=1

# 倒刺射击 - 集中值不足时优先回复
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=focus<=40&charges>=1

# 杀戮命令 - 正常使用，不过度限制集中值
actions.st+=/kill_command,if=charges>=1

# 倒刺射击 - 维持3层BUFF和集中值回复
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=charges>=1

# 眼镜蛇射击 - 重置杀戮命令和集中值回复
actions.st+=/cobra_shot

# 多目标循环（无黑蚀箭天赋）- 核心机制：杀戮命令+倒刺射击双核心输出

# 完美风暴状态（猎群领袖之嚎特殊组合）
actions.cleave=kill_command,if=variable.perfect_storm&action.kill_command.charges>=1&active_enemies<=6
actions.cleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=variable.perfect_storm&action.barbed_shot.charges>=1&debuff.barbed_shot.stack<2

# 爆发技能 - CD好了就用，不等待
actions.cleave+=/bestial_wrath
actions.cleave+=/call_of_the_wild
actions.cleave+=/bloodshed

# 多重射击 - 维护宠物兽性切割BUFF，狂野怒火期间不补（自带顺劈效果），BUFF剩余1秒内或没有BUFF时补充
actions.cleave+=/multishot,if=pet.main.buff.beast_cleave.remains<=1&!buff.bestial_wrath.up

# 狂野怒火期间多目标优化循环
# 倒刺射击 - 防止溢出充能（狂野怒火期间也要防溢出）
actions.cleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=action.barbed_shot.charges>=2
actions.cleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=full_recharge_time<gcd

# 爆炸射击 - 配合雷鸣蹄声天赋
actions.cleave+=/explosive_shot,if=talent.thundering_hooves&talent.explosive_shot

# 狂野怒火期间：集中值充足时，倒刺1层充能也要优先杀戮命令
actions.cleave+=/kill_command,if=buff.bestial_wrath.up&focus>=50&charges>=1
# 狂野怒火期间：杀戮命令不够时用眼镜蛇重置
actions.cleave+=/cobra_shot,if=buff.bestial_wrath.up&focus>=50&action.kill_command.charges<1&action.barbed_shot.charges>=1
# 狂野怒火期间：集中值不够时才打倒刺回复集中值
actions.cleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=buff.bestial_wrath.up&focus<50&charges>=1

# 倒刺射击 - 集中值不足时优先回复
actions.cleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=focus<=40&charges>=1

# 杀戮命令 - 正常使用，不过度限制集中值
actions.cleave+=/kill_command,if=charges>=1

# 倒刺射击 - 维持3层BUFF和集中值回复
actions.cleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=charges>=1

# 眼镜蛇射击 - 重置杀戮命令和集中值回复
actions.cleave+=/cobra_shot

# 单体循环（有黑蚀箭天赋）- 核心机制：杀戮命令+倒刺射击双核心输出
actions.drst=kill_shot

# 爆发技能 - CD好了就用，不等待
actions.drst+=/bestial_wrath
actions.drst+=/call_of_the_wild
actions.drst+=/bloodshed

# 狂野怒火期间优化循环 - 基于集中值管理和技能联动的智能轮换
# 倒刺射击优先级 - 确保不溢出充能，维持集中值回复
actions.drst+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=action.barbed_shot.charges>=2
actions.drst+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=full_recharge_time<gcd

# 狂野怒火期间：集中值充足时，倒刺1层充能也要优先杀戮命令
actions.drst+=/kill_command,if=buff.bestial_wrath.up&focus>=50&charges>=1
# 狂野怒火期间：杀戮命令不够时用眼镜蛇重置
actions.drst+=/cobra_shot,if=buff.bestial_wrath.up&focus>=50&action.kill_command.charges<1&action.barbed_shot.charges>=1
# 狂野怒火期间：集中值不够时才打倒刺回复集中值
actions.drst+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=buff.bestial_wrath.up&focus<50&charges>=1

# 杀戮命令 - 有充能且集中值充足时使用
actions.drst+=/kill_command,if=focus>=30&charges>=1

# 倒刺射击 - 集中值管理和BUFF维护
actions.drst+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=focus<50|debuff.barbed_shot.stack<3
# 倒刺射击 - 有充能就用（集中值回复和宠物BUFF）
actions.drst+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=charges>=1

# 眼镜蛇射击 - 集中值不足或重置杀戮命令时使用
actions.drst+=/cobra_shot,if=focus<30|action.kill_command.charges<1&action.barbed_shot.charges<1

# 多目标循环（有黑蚀箭天赋）- 核心机制：杀戮命令+倒刺射击双核心输出，AOE不使用夺命射击

# 爆发技能 - CD好了就用，不等待
actions.drcleave=bestial_wrath
actions.drcleave+=/call_of_the_wild
actions.drcleave+=/bloodshed

# 多重射击 - 维护宠物BUFF，狂野怒火期间不补（自带顺劈效果），BUFF剩余1秒内或没有BUFF时补充
actions.drcleave+=/multishot,if=pet.main.buff.beast_cleave.remains<=1&!buff.bestial_wrath.up

# 狂野怒火期间多目标优化循环 - 基于集中值管理和技能联动的智能轮换
# 倒刺射击优先级 - 确保不溢出充能，维持集中值回复
actions.drcleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=action.barbed_shot.charges>=2
actions.drcleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=full_recharge_time<gcd

# 爆炸射击 - 配合雷鸣蹄声
actions.drcleave+=/explosive_shot,if=talent.thundering_hooves&talent.explosive_shot

# 狂野怒火期间：集中值充足时，倒刺1层充能也要优先杀戮命令
actions.drcleave+=/kill_command,if=buff.bestial_wrath.up&focus>=50&charges>=1
# 狂野怒火期间：杀戮命令不够时用眼镜蛇重置
actions.drcleave+=/cobra_shot,if=buff.bestial_wrath.up&focus>=50&action.kill_command.charges<1&action.barbed_shot.charges>=1
# 狂野怒火期间：集中值不够时才打倒刺回复集中值
actions.drcleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=buff.bestial_wrath.up&focus<50&charges>=1

# 杀戮命令 - 有充能且集中值充足时使用
actions.drcleave+=/kill_command,if=focus>=30&charges>=1

# 倒刺射击 - 集中值管理和BUFF维护
actions.drcleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=focus<50|debuff.barbed_shot.stack<3
# 倒刺射击 - 有充能就用（集中值回复和宠物BUFF）
actions.drcleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=charges>=1

# 眼镜蛇射击 - 集中值不足或重置杀戮命令时使用
actions.drcleave+=/cobra_shot,if=focus<30|action.kill_command.charges<1&action.barbed_shot.charges<1

# 爆炸射击 - 收尾技能
actions.drcleave+=/explosive_shot,if=talent.explosive_shot

# 饰品管理
# 爆发期同步判断 - 扩大爆发窗口，增加更多同步机会
actions.trinkets=variable,name=pack_leader_sync_ready,value=talent.call_of_the_wild&(prev_gcd.1.call_of_the_wild|cooldown.call_of_the_wild.remains<3)|talent.bloodshed&(prev_gcd.1.bloodshed|cooldown.bloodshed.remains<3)|(!talent.call_of_the_wild&!talent.bloodshed)&(buff.bestial_wrath.up|cooldown.bestial_wrath.remains<5)|buff.hunters_prey.up
# 爆发期剩余时间计算
actions.trinkets+=/variable,name=pack_leader_sync_remains,op=setif,value=cooldown.bestial_wrath.remains,value_else=cooldown.call_of_the_wild.remains|cooldown.bloodshed.remains,condition=!talent.call_of_the_wild&!talent.bloodshed
# 当前爆发期状态 - 扩大爆发期判断范围
actions.trinkets+=/variable,name=pack_leader_sync_active,value=buff.call_of_the_wild.up|buff.bloodshed.up|buff.bestial_wrath.up|buff.lead_from_the_front.up|buff.hunters_prey.up|buff.aspect_of_the_wild.up
# 伤害同步状态
actions.trinkets+=/variable,name=damage_sync_active,value=variable.pack_leader_sync_active
actions.trinkets+=/variable,name=damage_sync_remains,value=variable.pack_leader_sync_remains

# 猎群领袖专用饰品使用逻辑 - 简化并修复同步逻辑
# 主要饰品使用 - 与爆发期同步，考虑集中值状态
actions.trinkets+=/use_item,slot=trinket1,if=trinket.1.has_use_buff&(variable.pack_leader_sync_ready|variable.pack_leader_sync_active)&(variable.stronger_trinket_slot=1|trinket.2.cooldown.remains>20)&focus>=60|!trinket.1.has_use_buff&(variable.stronger_trinket_slot=1|trinket.2.cooldown.remains)&(trinket.1.cast_time=0|!variable.pack_leader_sync_active)|fight_remains<25&variable.stronger_trinket_slot=1
# 次要饰品使用 - 与爆发期同步，考虑集中值状态
actions.trinkets+=/use_item,slot=trinket2,if=trinket.2.has_use_buff&(variable.pack_leader_sync_ready|variable.pack_leader_sync_active)&(variable.stronger_trinket_slot=2|trinket.1.cooldown.remains>20)&focus>=60|!trinket.2.has_use_buff&(variable.stronger_trinket_slot=2|trinket.1.cooldown.remains)&(trinket.2.cast_time=0|!variable.pack_leader_sync_active)|fight_remains<25&variable.stronger_trinket_slot=2