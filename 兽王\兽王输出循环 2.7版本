#优化日期：2025-08-28 - 参考文件1复杂逻辑优化版本
hunter="兽王魔兽世界优化v2.5"
source=default
spec=beast_mastery
level=80
race=human
role=attack
position=ranged_back
professions=leatherworking=69/engineering=27
talents=C0PA11Mk8aZ38kAf+zso3nZ9IAMmxwCZDmRDNsBAAAAAgxMm5BGzMDmZGsMjZmxYmZmMjZMzMzgZGGGjZmhZbYYmhNAAAAAAYGA

# 消耗品配置 - 仅保留实际使用的消耗品
potion=tempered_potion_3
food=the_sushi_special

# 战前准备
actions.precombat=summon_pet
actions.precombat+=/snapshot_stats
# 饰品强度判断变量 - 参考文件1的复杂饰品强度判断逻辑
actions.precombat+=/variable,name=trinket_1_stronger,value=!trinket.2.has_cooldown|trinket.1.has_use_buff&(!trinket.2.has_use_buff|!trinket.1.is.mirror_of_fractured_tomorrows&(trinket.2.is.mirror_of_fractured_tomorrows|trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration))|!trinket.1.has_use_buff&(!trinket.2.has_use_buff&(trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration))
actions.precombat+=/variable,name=trinket_2_stronger,value=!variable.trinket_1_stronger

# 猎群领袖爆发期状态变量
actions.precombat+=/variable,name=pack_leader_burst,value=0
actions.precombat+=/variable,name=trinket_window,value=0
actions.precombat+=/variable,name=cotw_window,value=0
actions.precombat+=/variable,name=burst_incoming,value=0

# 兽王核心变量系统 - 参考文件1的buff同步机制
actions.precombat+=/variable,name=bear_priority,value=buff.howl_of_the_pack_leader_bear.up|cooldown.pack_leader.remains<8
actions.precombat+=/variable,name=perfect_storm,value=buff.howl_of_the_pack_leader_bear.up&buff.bestial_wrath.up
actions.precombat+=/variable,name=focus_threshold_high,value=75
actions.precombat+=/variable,name=focus_threshold_low,value=40
actions.precombat+=/variable,name=burst_window_active,value=buff.bestial_wrath.up|cooldown.bestial_wrath.remains<3

# 饰品同步系统 - 只等待猎群领袖之嚎和狂野怒火，不等血腥撕裂
actions.precombat+=/variable,name=buff_sync_ready,value=buff.howl_of_the_pack_leader_bear.up|buff.howl_of_the_pack_leader_wolf.up|buff.bestial_wrath.up|cooldown.bestial_wrath.remains<3
# 计算下次关键buff的剩余冷却时间
actions.precombat+=/variable,name=buff_sync_remains,value=cooldown.bestial_wrath.remains
# 判断当前是否有关键buff激活
actions.precombat+=/variable,name=buff_sync_active,value=buff.howl_of_the_pack_leader_bear.up|buff.howl_of_the_pack_leader_wolf.up|buff.bestial_wrath.up
# 嚎叫召唤准备状态判断
actions.precombat+=/variable,name=howl_summon_ready,value=buff.howl_of_the_pack_leader_cooldown.remains<8&!buff.howl_of_the_pack_leader_bear.up&!buff.howl_of_the_pack_leader_wolf.up

# 起手爆发优化 - 倒刺→杀戮→倒刺→爆发技能
actions.precombat+=/barbed_shot,if=!buff.hunter_master.up|buff.hunter_master.stack<5
actions.precombat+=/kill_command,if=buff.hunter_master.stack>=4&buff.hunter_master.stack<5
actions.precombat+=/barbed_shot,if=buff.hunter_master.stack>=4&action.barbed_shot.charges>=1
actions.precombat+=/bestial_wrath

# 主循环
actions=auto_shot
# 反制技能优先级
actions+=/counter_shot
actions+=/tranquilizing_shot
# 保命技能优先级 - 血线管理
actions+=/primal_instincts,if=health.pct<=60
actions+=/exhilaration,if=health.pct<=50
actions+=/aspect_of_the_turtle,if=health.pct<=15
# 修复后的治疗宠物逻辑
actions+=/mend_pet,if=pet.health_pct<pet_healing
# 猎人印记管理
actions+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20

# 动态变量实时更新 - 参考文件1的复杂同步逻辑
actions+=/variable,name=perfect_storm,value=buff.howl_of_the_pack_leader_bear.up&buff.bestial_wrath.up
actions+=/variable,name=burst_window_active,value=buff.bestial_wrath.up|cooldown.bestial_wrath.remains<3
actions+=/variable,name=focus_starved,value=focus<variable.focus_threshold_low&focus.regen<15
# 实时更新buff同步状态
actions+=/variable,name=buff_sync_ready,value=buff.howl_of_the_pack_leader_bear.up|buff.howl_of_the_pack_leader_wolf.up|buff.bestial_wrath.up|cooldown.bestial_wrath.remains<5
actions+=/variable,name=buff_sync_active,value=buff.howl_of_the_pack_leader_bear.up|buff.howl_of_the_pack_leader_wolf.up|buff.bestial_wrath.up
actions+=/variable,name=howl_summon_ready,value=buff.howl_of_the_pack_leader_cooldown.remains<8&!buff.howl_of_the_pack_leader_bear.up&!buff.howl_of_the_pack_leader_wolf.up
# 猎群领袖爆发期管理
actions+=/variable,name=pack_leader_burst,value=buff.call_of_the_wild.up|buff.bestial_wrath.up|buff.bloodshed.up|buff.lead_from_the_front.up|buff.hunters_prey.up|cooldown.call_of_the_wild.remains<3|cooldown.bestial_wrath.remains<3|cooldown.bloodshed.remains<3
actions+=/variable,name=burst_incoming,value=cooldown.call_of_the_wild.remains<5|cooldown.bestial_wrath.remains<5|cooldown.bloodshed.remains<5

actions+=/call_action_list,name=cds
actions+=/call_action_list,name=trinkets
# 参考官方推导的目标数量判断逻辑
actions+=/call_action_list,name=drst,if=talent.black_arrow&(active_enemies<2|!talent.beast_cleave&active_enemies<3)
actions+=/call_action_list,name=drcleave,if=talent.black_arrow&(active_enemies>2|talent.beast_cleave&active_enemies>1)
actions+=/call_action_list,name=st,if=!talent.black_arrow&(active_enemies<2|!talent.beast_cleave&active_enemies<3)
actions+=/call_action_list,name=cleave,if=!talent.black_arrow&(active_enemies>2|talent.beast_cleave&active_enemies>1)

# CD技能管理 - 参考文件1优先与"野性呼唤"buff同步使用
actions.cds=invoke_external_buff,name=power_infusion,if=buff.call_of_the_wild.up|talent.bloodshed&(prev_gcd.1.bloodshed)|!talent.call_of_the_wild&(buff.bestial_wrath.up|cooldown.bestial_wrath.remains<30)|fight_remains<16
# 种族技能 - 优先与野性呼唤同步，其次与狂野怒火同步
actions.cds+=/berserking,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|boss&fight_remains<13
actions.cds+=/blood_fury,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|boss&fight_remains<16
actions.cds+=/ancestral_call,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|boss&fight_remains<16
actions.cds+=/fireblood,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|boss&fight_remains<9
actions.cds+=/potion,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|boss&fight_remains<31

# 单体循环（无黑蚀箭天赋）- 核心机制：杀戮命令+倒刺射击双核心输出
# 单体循环起始 - 不使用夺命射击
actions.st=kill_command,if=!cooldown.bestial_wrath.ready

# 完美风暴状态（猎群领袖之嚎特殊组合）- 杀戮命令输出极高期间
actions.st+=/kill_command,if=variable.perfect_storm&action.kill_command.charges>=1&!cooldown.bestial_wrath.ready
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=variable.perfect_storm&action.barbed_shot.charges>=1&debuff.barbed_shot.stack<1

# 爆发技能 - 参考文件1的4件套时机判断
actions.st+=/bestial_wrath
actions.st+=/call_of_the_wild
actions.st+=/bloodshed

# 倒刺射击 - 狂野怒火前加速消耗以缩短冷却
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=(cooldown.bestial_wrath.remains<=12&action.barbed_shot.charges>=1)|(cooldown.bestial_wrath.remains<=24&action.barbed_shot.charges>=2)
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=cooldown.bestial_wrath.remains<=24&prev_gcd.1.barbed_shot&action.barbed_shot.charges>=1


# 倒刺射击 - 参考文件1更复杂的使用条件，包括与野性呼唤和嚎叫召唤的协调
actions.st+=/barbed_shot,cycle_targets=1,if=full_recharge_time<gcd|charges_fractional>=cooldown.kill_command.charges_fractional|talent.call_of_the_wild&cooldown.call_of_the_wild.ready|variable.howl_summon_ready&full_recharge_time<8
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=action.barbed_shot.charges>=2

# 杀戮指令 - 参考文件1复杂的充能管理逻辑，避免与前线领袖buff冲突
actions.st+=/kill_command,if=charges_fractional>=cooldown.barbed_shot.charges_fractional&!(buff.lead_from_the_front.remains>gcd&buff.lead_from_the_front.remains<gcd*2&!variable.howl_summon_ready&full_recharge_time>gcd)&!cooldown.bestial_wrath.ready

# 狂野怒火期间：集中值充足时，倒刺1层充能也要优先杀戮命令
actions.st+=/kill_command,if=buff.bestial_wrath.up&charges>=1
# 狂野怒火期间：杀戮命令不够时用眼镜蛇重置
actions.st+=/cobra_shot,if=buff.bestial_wrath.up&focus>=50&action.kill_command.charges<1&action.barbed_shot.charges>=1
# 狂野怒火期间：集中值不够时才打倒刺回复集中值
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=buff.bestial_wrath.up&focus<50&charges>=1

# 杀戮命令 - 熊在场时优先使用
actions.st+=/kill_command,if=buff.howl_of_the_pack_leader_bear.up&charges>=1&!cooldown.bestial_wrath.ready

# 倒刺射击 - 集中值不足时优先回复
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=focus<=40&charges>=1

# 杀戮命令 - 正常使用，不过度限制集中值
actions.st+=/kill_command,if=charges>=1&!cooldown.bestial_wrath.ready

# 倒刺射击 - 维持3层BUFF和集中值回复
actions.st+=/barbed_shot,cycle_targets=1

# 眼镜蛇射击 - 参考文件1增加了野猪骑士层数和多重射击天赋的判断
actions.st+=/cobra_shot,if=focus.time_to_max<gcd*2|buff.hogstrider.stack>3|!talent.multishot

# 多目标循环（无黑蚀箭天赋）- 核心机制：杀戮命令+倒刺射击双核心输出

# 完美风暴状态（猎群领袖之嚎特殊组合）
actions.cleave=kill_command,if=variable.perfect_storm&action.kill_command.charges>=1&active_enemies<=6&!cooldown.bestial_wrath.ready
actions.cleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=variable.perfect_storm&action.barbed_shot.charges>=1&debuff.barbed_shot.stack<2

# 爆发技能 - 参考文件1的4件套和多重射击天赋判断
actions.cleave+=/bestial_wrath
actions.cleave+=/call_of_the_wild
actions.cleave+=/bloodshed

# 倒刺射击 - 红人前预消耗 + 2.3防溢出（多目标）
actions.cleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=(cooldown.bestial_wrath.remains<=12&action.barbed_shot.charges>=1)|(cooldown.bestial_wrath.remains<=24&action.barbed_shot.charges>=2)
actions.cleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=cooldown.bestial_wrath.remains<=24&prev_gcd.1.barbed_shot&action.barbed_shot.charges>=1
# 2.3防溢出：两层时直接打
actions.cleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=action.barbed_shot.charges>=2

# 倒刺射击 - 综合条件
actions.cleave+=/barbed_shot,cycle_targets=1,if=full_recharge_time<gcd|charges_fractional>=cooldown.kill_command.charges_fractional|talent.call_of_the_wild&cooldown.call_of_the_wild.ready|variable.howl_summon_ready&full_recharge_time<8

# 多重射击 - 维护宠物兽性切割BUFF，狂野怒火期间不使用（8秒内自动多重效果）
actions.cleave+=/multishot,if=pet.main.buff.beast_cleave.down&!buff.bestial_wrath.up&(!talent.bloody_frenzy|cooldown.call_of_the_wild.remains)

# 爆炸射击 - 配合雷鸣蹄声天赋
actions.cleave+=/explosive_shot,if=talent.thundering_hooves

# 杀戮命令 - 参考文件1的优先级调整
actions.cleave+=/kill_command,if=!cooldown.bestial_wrath.ready

# 狂野怒火期间：集中值充足时，倒刺1层充能也要优先杀戮命令
actions.cleave+=/kill_command,if=buff.bestial_wrath.up&charges>=1
# 狂野怒火期间：杀戮命令不够时用眼镜蛇重置
actions.cleave+=/cobra_shot,if=buff.bestial_wrath.up&focus>=50&action.kill_command.charges<1&action.barbed_shot.charges>=1
# 狂野怒火期间：集中值不够时才打倒刺回复集中值
actions.cleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=buff.bestial_wrath.up&focus<50&charges>=1

# 倒刺射击 - 集中值不足时优先回复
actions.cleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=focus<=40&charges>=1

# 倒刺射击 - 维持3层BUFF和集中值回复
actions.cleave+=/barbed_shot,cycle_targets=1

# 眼镜蛇射击 - 参考文件1增加了野猪骑士层数和多重射击天赋的判断
actions.cleave+=/cobra_shot,if=focus.time_to_max<gcd*2|buff.hogstrider.stack>3|!talent.multishot

# 单体循环（有黑蚀箭天赋）- 参考文件1的黑箭循环特点
actions.drst=kill_command,if=!cooldown.bestial_wrath.ready

# 爆发技能 - 参考文件1的保守使用策略，避免与野性呼唤冲突
actions.drst+=/bestial_wrath
actions.drst+=/call_of_the_wild
actions.drst+=/bloodshed
actions.drst+=/kill_command,if=buff.bestial_wrath.up&charges>=1

# 倒刺射击 - 红人前预消耗 + 2.3防溢出（黑蚀箭单体）
actions.drst+=/barbed_shot,cycle_targets=1,if=full_recharge_time<gcd
# 2.3防溢出：两层时直接打
actions.drst+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=action.barbed_shot.charges>=2

# 杀戮命令 - 参考文件1的简化优先级
actions.drst+=/kill_command,if=!cooldown.bestial_wrath.ready

# 倒刺射击 - 参考文件1的常规使用
actions.drst+=/barbed_shot,cycle_targets=1

# 眼镜蛇射击 - 参考文件1的填充技能
actions.drst+=/cobra_shot

# 多目标循环（有黑蚀箭天赋）- 参考文件1的黑箭多目标循环

# 爆发技能 - 参考文件1的保守使用策略
actions.drcleave=kill_command,if=!cooldown.bestial_wrath.ready
actions.drcleave+=/bestial_wrath
actions.drcleave+=/call_of_the_wild
actions.drcleave+=/bloodshed
actions.drcleave+=/kill_command,if=buff.bestial_wrath.up&charges>=1

# 倒刺射击 - 红人前预消耗 + 2.3防溢出（黑蚀箭多目标）
actions.drcleave+=/barbed_shot,cycle_targets=1,if=full_recharge_time<gcd
# 2.3防溢出：两层时直接打
actions.drcleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=action.barbed_shot.charges>=2

# 多重射击 - 维护宠物BUFF，狂野怒火期间不使用（8秒内自动多重效果）
actions.drcleave+=/multishot,if=pet.main.buff.beast_cleave.down&!buff.bestial_wrath.up&(!talent.bloody_frenzy|cooldown.call_of_the_wild.remains)

# 爆炸射击 - 配合雷鸣蹄声
actions.drcleave+=/explosive_shot,if=talent.thundering_hooves

# 倒刺射击 - 参考文件1的充能管理
actions.drcleave+=/barbed_shot,cycle_targets=1,if=charges_fractional>=cooldown.kill_command.charges_fractional

# 杀戮命令 - 参考文件1的简化优先级
actions.drcleave+=/kill_command,if=!cooldown.bestial_wrath.ready

# 眼镜蛇射击 - 参考文件1的集中值管理
actions.drcleave+=/cobra_shot,if=focus.time_to_max<gcd*2

# 爆炸射击 - 收尾技能
actions.drcleave+=/explosive_shot

# 饰品管理 - 只等待猎群领袖之嚎与狂野怒火
# 同步变量系统（不等待血腥撕裂）
actions.trinkets=variable,name=buff_sync_ready,value=buff.howl_of_the_pack_leader_bear.up|buff.howl_of_the_pack_leader_wolf.up|buff.bestial_wrath.up|cooldown.bestial_wrath.remains_guess<5
actions.trinkets+=/variable,name=buff_sync_remains,value=cooldown.bestial_wrath.remains_guess
actions.trinkets+=/variable,name=buff_sync_active,value=buff.howl_of_the_pack_leader_bear.up|buff.howl_of_the_pack_leader_wolf.up|buff.bestial_wrath.up
actions.trinkets+=/variable,name=damage_sync_active,value=1
actions.trinkets+=/variable,name=damage_sync_remains,value=0

# 参考文件1的复杂饰品使用逻辑 - 考虑饰品强度、buff同步、冷却时间等多重因素
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket1,if=trinket.1.has_use_buff&(variable.buff_sync_ready&(variable.trinket_1_stronger|trinket.2.cooldown.remains)|!variable.buff_sync_ready&(variable.trinket_1_stronger&(variable.buff_sync_remains>trinket.1.cooldown.duration%3&boss&fight_remains>trinket.1.cooldown.duration+20|trinket.2.has_use_buff&trinket.2.cooldown.remains>variable.buff_sync_remains-15&trinket.2.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains+45>fight_remains&boss)|variable.trinket_2_stronger&(trinket.2.cooldown.remains&(trinket.2.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains>=20|trinket.2.cooldown.remains-5>=variable.buff_sync_remains&(variable.buff_sync_remains>trinket.1.cooldown.duration%3|trinket.1.cooldown.duration<fight_remains&boss&(variable.buff_sync_remains+trinket.1.cooldown.duration>fight_remains)))|trinket.2.cooldown.ready&variable.buff_sync_remains>20&variable.buff_sync_remains<trinket.2.cooldown.duration%3)))|!trinket.1.has_use_buff&(trinket.1.cast_time=0|!variable.buff_sync_active)&(!trinket.2.has_use_buff&(variable.trinket_1_stronger|trinket.2.cooldown.remains)|trinket.2.has_use_buff&(!variable.buff_sync_active&variable.buff_sync_remains>20|trinket.2.cooldown.remains>20))|boss&fight_remains<25&(variable.trinket_1_stronger|trinket.2.cooldown.remains)

actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket2,if=trinket.2.has_use_buff&(variable.buff_sync_ready&(variable.trinket_2_stronger|trinket.1.cooldown.remains)|!variable.buff_sync_ready&(variable.trinket_2_stronger&(variable.buff_sync_remains>trinket.2.cooldown.duration%3&boss&fight_remains>trinket.2.cooldown.duration+20|trinket.1.has_use_buff&trinket.1.cooldown.remains>variable.buff_sync_remains-15&trinket.1.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains+45>fight_remains&boss)|variable.trinket_1_stronger&(trinket.1.cooldown.remains&(trinket.1.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains>=20|trinket.1.cooldown.remains-5>=variable.buff_sync_remains&(variable.buff_sync_remains>trinket.2.cooldown.duration%3|trinket.2.cooldown.duration<fight_remains&boss&(variable.buff_sync_remains+trinket.2.cooldown.duration>fight_remains)))|trinket.1.cooldown.ready&variable.buff_sync_remains>20&variable.buff_sync_remains<trinket.1.cooldown.duration%3)))|!trinket.2.has_use_buff&(trinket.2.cast_time=0|!variable.buff_sync_active)&(!trinket.1.has_use_buff&(variable.trinket_2_stronger|trinket.1.cooldown.remains)|trinket.1.has_use_buff&(!variable.buff_sync_active&variable.buff_sync_remains>20|trinket.1.cooldown.remains>20))|boss&fight_remains<25&(variable.trinket_2_stronger|trinket.1.cooldown.remains)