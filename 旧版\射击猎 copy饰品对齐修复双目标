## Date: 2025-08-15
## 射击猎人手法 - 优化版本

# ========== 预战斗设置 ==========
actions.precombat+=/summon_pet,if=talent.unbreakable_bond

# 饰品优先级判断 - 进一步简化
actions.precombat+=/variable,name=trinket_1_stronger,value=trinket.1.has_use_buff&!trinket.2.has_use_buff|trinket.1.has_use_buff&trinket.2.has_use_buff&trinket.1.cooldown.duration>=trinket.2.cooldown.duration|!trinket.1.has_use_buff&!trinket.2.has_use_buff&trinket.1.cooldown.duration>=trinket.2.cooldown.duration
actions.precombat+=/variable,name=trinket_2_stronger,value=!variable.trinket_1_stronger

# 核心变量定义 - 统一管理
actions.precombat+=/variable,name=pet_healing_threshold,value=40
actions.precombat+=/variable,name=aimed_shot_base_cost,value=35
actions.precombat+=/variable,name=focus_threshold_high,value=60
actions.precombat+=/variable,name=focus_threshold_low,value=30

# 完美射击窗口定义 - 多BUFF同步
actions.precombat+=/variable,name=perfect_shot_window,value=buff.trueshot.up&buff.bulletstorm.up&(buff.lunar_storm.up|!hero_tree.sentinel)

actions.precombat+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20

# 预战斗技能 - 根据天赋优化
actions.precombat+=/aimed_shot,if=active_enemies<3|talent.black_arrow&talent.headshot
actions.precombat+=/steady_shot

# ========== 主循环 ==========
# 动态变量更新 - 实时计算资源状态
actions+=/variable,name=aimed_shot_focus_cost,value=variable.aimed_shot_base_cost*(1-0.5*buff.trueshot.up)
actions+=/variable,name=burst_phase,value=buff.trueshot.up|(buff.lunar_storm.up&hero_tree.sentinel)
actions+=/variable,name=focus_ok,value=focus>=variable.aimed_shot_focus_cost
actions+=/variable,name=trueshot_ready,value=cooldown.trueshot.ready&(!talent.bullseye|fight_remains>25|buff.bullseye.stack=buff.bullseye.max_stack)&(!trinket.1.has_use_buff|trinket.1.cooldown.remains>30|trinket.1.cooldown.ready)&(!trinket.2.has_use_buff|trinket.2.cooldown.remains>30|trinket.2.cooldown.ready)|boss&fight_remains<25

# 打断和驱散
actions+=/counter_shot
actions+=/tranquilizing_shot

# 生存技能 - 优化触发条件
actions+=/mend_pet,if=pet.health_pct<variable.pet_healing_threshold
actions+=/exhilaration,if=health.pct<40&!variable.burst_phase
actions+=/aspect_of_the_cheetah,if=!in_combat&!buff.aspect_of_the_cheetah.up

# 猎人印记维护
actions+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20

# 循环优先级 - 严格目标数量判断
actions+=/call_action_list,name=burst,if=variable.burst_phase|variable.trueshot_ready
actions+=/call_action_list,name=cds
actions+=/call_action_list,name=trinkets
actions+=/call_action_list,name=trickshots,strict=1,if=active_enemies>=3&talent.trick_shots
actions+=/call_action_list,name=cleave,strict=1,if=active_enemies=2|active_enemies>2&!talent.trick_shots
actions+=/call_action_list,name=st,if=active_enemies=1

# ========== 冷却技能管理 ==========
# 种族技能 - 简化条件判断
actions.cds+=/berserking,if=variable.burst_phase|boss&fight_remains<15
actions.cds+=/blood_fury,if=variable.burst_phase|cooldown.trueshot.remains>30|boss&fight_remains<20
actions.cds+=/ancestral_call,if=variable.burst_phase|cooldown.trueshot.remains>30|boss&fight_remains<20
actions.cds+=/fireblood,if=variable.burst_phase|cooldown.trueshot.remains>30|boss&fight_remains<15
actions.cds+=/lights_judgment,if=!variable.burst_phase

# 药水使用 - 优化同步时机
actions.cds+=/potion,if=variable.burst_phase&(buff.bloodlust.up|target.health.pct<20)|boss&fight_remains<35

# ========== 爆发循环 - BUG利用优化版 ==========
# 单体爆发 - 严格瞄准→爆炸顺序
actions.burst+=/trueshot,if=active_enemies=1&variable.trueshot_ready&!buff.trueshot.up
actions.burst+=/aimed_shot,if=active_enemies=1&variable.burst_phase&action.aimed_shot.charges>=1&variable.focus_ok
actions.burst+=/explosive_shot,if=active_enemies=1&variable.burst_phase&prev_gcd.1.aimed_shot&talent.precision_detonation
actions.burst+=/rapid_fire,if=active_enemies=1&variable.burst_phase&(buff.bulletstorm.remains<3|!buff.bulletstorm.up)&!prev_gcd.1.aimed_shot
actions.burst+=/aimed_shot,if=active_enemies=1&variable.burst_phase&action.aimed_shot.charges>=1&variable.focus_ok&!prev_gcd.1.aimed_shot
actions.burst+=/explosive_shot,if=active_enemies=1&variable.burst_phase&prev_gcd.1.aimed_shot&!talent.precision_detonation
actions.burst+=/arcane_shot,if=active_enemies=1&variable.burst_phase&!variable.focus_ok&action.aimed_shot.charges<1

# 双目标爆发 - 瞄准→爆炸连招优化
actions.burst+=/multishot,if=active_enemies=2&!buff.trick_shots.up
actions.burst+=/trueshot,if=active_enemies=2&variable.trueshot_ready&!buff.trueshot.up
actions.burst+=/aimed_shot,if=active_enemies=2&variable.burst_phase&action.aimed_shot.charges>=1&variable.focus_ok
actions.burst+=/explosive_shot,if=active_enemies=2&variable.burst_phase&prev_gcd.1.aimed_shot
actions.burst+=/volley,if=active_enemies=2&variable.burst_phase&talent.volley&!prev_gcd.1.aimed_shot
actions.burst+=/rapid_fire,if=active_enemies=2&variable.burst_phase&(buff.bulletstorm.remains<3|!buff.bulletstorm.up)&!prev_gcd.1.aimed_shot
actions.burst+=/multishot,if=active_enemies=2&variable.burst_phase&!prev_gcd.1.aimed_shot

# AOE爆发循环 - 瞄准→爆炸连招优化
actions.burst+=/multishot,if=active_enemies>=3&!buff.trick_shots.up
actions.burst+=/trueshot,if=active_enemies>=3&variable.trueshot_ready&!buff.trueshot.up&(buff.trick_shots.up|!talent.trick_shots)
actions.burst+=/aimed_shot,if=active_enemies>=3&variable.burst_phase&(buff.trick_shots.up|!talent.trick_shots)&action.aimed_shot.charges>=1&variable.focus_ok
actions.burst+=/explosive_shot,if=active_enemies>=3&variable.burst_phase&(buff.trick_shots.up|!talent.trick_shots)&prev_gcd.1.aimed_shot
actions.burst+=/rapid_fire,if=active_enemies>=3&variable.burst_phase&(buff.trick_shots.up|!talent.trick_shots)&!buff.bulletstorm.up&!prev_gcd.1.aimed_shot
actions.burst+=/volley,if=active_enemies>=3&variable.burst_phase&(buff.trick_shots.up|!talent.trick_shots)&talent.volley&!prev_gcd.1.aimed_shot
actions.burst+=/multishot,if=active_enemies>=3&variable.burst_phase&(buff.trick_shots.up|!talent.trick_shots)&!prev_gcd.1.aimed_shot

# ========== 双目标循环 - BUG利用优化版（修复无效多重射击） ==========
# 夺命射击最高优先级 - 斩杀阶段和BUFF期，避免打断连招
actions.cleave+=/kill_shot,cycle_targets=1,if=(talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)|!talent.headshot&buff.razor_fragments.up)&!prev_gcd.1.aimed_shot

# 黑蚀箭优先级 - 在瞄准射击前，避免打断连招
actions.cleave+=/black_arrow,if=buff.precise_shots.up&buff.moving_target.down&variable.trueshot_ready&!prev_gcd.1.aimed_shot
actions.cleave+=/black_arrow,if=(!talent.headshot|talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down))&!prev_gcd.1.aimed_shot

# 百发百中开启 - 避免打断连招
actions.cleave+=/trueshot,if=variable.trueshot_ready&!variable.burst_phase&!prev_gcd.1.aimed_shot

# 哨兵天赋月之风暴优化 - 避免打断连招
actions.cleave+=/rapid_fire,if=talent.lunar_storm&buff.lunar_storm_cooldown.down&hero_tree.sentinel&action.aimed_shot.charges>=1&!prev_gcd.1.aimed_shot
actions.cleave+=/aimed_shot,cycle_targets=1,if=hero_tree.sentinel&buff.lunar_storm.up&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&(buff.streamline.stack>=1|focus>=50)&!prev_gcd.1.aimed_shot

# 瞄准射击优先级 - 为爆炸射击做准备
actions.cleave+=/aimed_shot,cycle_targets=1,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&full_recharge_time<action.rapid_fire.execute_time+cast_time&(!talent.bulletstorm|buff.bulletstorm.up)&talent.windrunner_quiver&!prev_gcd.1.aimed_shot
actions.cleave+=/aimed_shot,cycle_targets=1,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&!variable.burst_phase&action.aimed_shot.charges>=1&!prev_gcd.1.aimed_shot

# 爆炸射击 - 严格在瞄准射击后
actions.cleave+=/explosive_shot,if=talent.precision_detonation&prev.1.aimed_shot&!variable.burst_phase
actions.cleave+=/explosive_shot,if=prev.1.aimed_shot&!variable.burst_phase&cooldown.explosive_shot.ready

# 乱射优先级 - 双目标有效AOE技能，避免打断瞄准→爆炸连招
actions.cleave+=/volley,if=talent.volley&(buff.double_tap.down|!talent.double_tap)&!variable.burst_phase&!prev_gcd.1.aimed_shot

# 急速射击优化 - 避免打断连招
actions.cleave+=/rapid_fire,if=talent.bulletstorm&buff.bulletstorm.down&!variable.burst_phase&!prev_gcd.1.aimed_shot
actions.cleave+=/rapid_fire,if=!variable.burst_phase&(buff.bulletstorm.remains<3|!buff.bulletstorm.up)&!prev_gcd.1.aimed_shot

# 填充技能优化 - 移除无效多重射击，优先奥术射击，避免打断连招
actions.cleave+=/arcane_shot,cycle_targets=1,if=buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)&!prev_gcd.1.aimed_shot

# 最后的集中值管理 - 仅在必要时使用稳固射击，避免打断连招
actions.cleave+=/steady_shot,if=focus<variable.focus_threshold_low&!variable.burst_phase&!prev_gcd.1.aimed_shot

# ========== 单体循环 - BUG利用优化版 ==========
# 黑暗游侠天赋优先级 - 黑蚀箭配合弹无虚发
actions.st+=/black_arrow,if=buff.precise_shots.stack>=2&hero_tree.dark_ranger

# 百发百中开启前准备 - 确保黑蚀箭已使用
actions.st+=/trueshot,if=variable.trueshot_ready&(!hero_tree.dark_ranger|!talent.black_arrow|!buff.black_arrow.up)

# 哨兵天赋月之风暴优化 - 避免打断瞄准→爆炸连招
actions.st+=/rapid_fire,if=talent.lunar_storm&buff.lunar_storm_cooldown.down&hero_tree.sentinel&action.aimed_shot.charges>=1&!prev_gcd.1.aimed_shot
actions.st+=/aimed_shot,if=hero_tree.sentinel&buff.lunar_storm.up&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&(buff.streamline.stack>=1|variable.focus_ok)

# 爆炸射击 - 严格在瞄准射击后，利用BUG
actions.st+=/explosive_shot,if=talent.precision_detonation&prev.1.aimed_shot&!variable.burst_phase
actions.st+=/explosive_shot,if=prev.1.aimed_shot&!variable.burst_phase&cooldown.explosive_shot.ready

# 夺命射击优化 - 避免打断连招
actions.st+=/kill_shot,if=(talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)|!talent.headshot&buff.razor_fragments.up|hero_tree.sentinel&buff.precise_shots.up&target.health.pct>20)&!prev_gcd.1.aimed_shot

# 黑蚀箭常规使用 - 避免打断连招
actions.st+=/black_arrow,if=(!talent.headshot|talent.headshot&buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down))&!prev_gcd.1.aimed_shot

# 瞄准射击优先级 - 为爆炸射击做准备
actions.st+=/aimed_shot,if=variable.perfect_shot_window&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&variable.focus_ok&(buff.streamline.stack>=2|variable.burst_phase)
actions.st+=/aimed_shot,if=variable.burst_phase&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&variable.focus_ok&!variable.perfect_shot_window
actions.st+=/aimed_shot,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&full_recharge_time<action.rapid_fire.execute_time+cast_time&(!talent.bulletstorm|buff.bulletstorm.up)&talent.windrunner_quiver
actions.st+=/aimed_shot,if=!variable.burst_phase&(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&buff.streamline.stack>=1&variable.focus_ok
actions.st+=/aimed_shot,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&(buff.streamline.stack>=1|variable.burst_phase)
actions.st+=/aimed_shot,if=action.aimed_shot.charges>=2

# 急速射击优化 - 避免打断瞄准→爆炸连招
actions.st+=/rapid_fire,if=active_enemies>=3&buff.bulletstorm.stack<20&execute_time>1&!prev_gcd.1.aimed_shot
actions.st+=/rapid_fire,if=(!variable.burst_phase|focus<50)&(buff.bulletstorm.remains<3|!buff.bulletstorm.up)&active_enemies<3&!prev_gcd.1.aimed_shot

# 填充技能优化 - 避免打断连招
actions.st+=/arcane_shot,if=buff.precise_shots.up&(debuff.spotters_mark.down|buff.moving_target.down)&(!hero_tree.dark_ranger|!talent.black_arrow|cooldown.black_arrow.remains>3)&!prev_gcd.1.aimed_shot
actions.st+=/explosive_shot,if=hero_tree.sentinel&buff.lunar_storm.up&buff.streamline.stack>=2&action.aimed_shot.charges>=1&!prev.1.aimed_shot
actions.st+=/explosive_shot,if=!variable.burst_phase&cooldown.explosive_shot.ready&!prev.1.aimed_shot&!talent.precision_detonation

# 集中值管理 - 优化预判，避免打断连招
actions.st+=/steady_shot,if=talent.black_arrow&focus+cast_regen<focus.max&prev.1.aimed_shot&!buff.deathblow.react&!variable.burst_phase&cooldown.trueshot.remains
actions.st+=/steady_shot,if=focus<variable.aimed_shot_focus_cost&action.aimed_shot.charges>=1&!variable.burst_phase&!prev_gcd.1.aimed_shot
actions.st+=/steady_shot,if=(focus<variable.focus_threshold_high|focus.time_to_max<3)&!prev_gcd.1.aimed_shot

# ========== 多目标循环 - BUG利用优化版 ==========
# 开场优先级 - 确保技巧射击BUFF，避免稳固射击开头
actions.trickshots+=/multishot,cycle_targets=1,if=buff.trick_shots.down|buff.trick_shots.remains<gcd.max
actions.trickshots+=/multishot,cycle_targets=1,if=!buff.trick_shots.up&active_enemies>=3

# 百发百中开启
actions.trickshots+=/trueshot,if=variable.trueshot_ready&buff.double_tap.down&buff.trick_shots.up

# 黑蚀箭使用 - 避免打断瞄准→爆炸连招
actions.trickshots+=/black_arrow,if=(!talent.headshot|buff.precise_shots.up|buff.trick_shots.down)&!prev_gcd.1.aimed_shot

# 瞄准射击优先级 - 为爆炸射击做准备
actions.trickshots+=/aimed_shot,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&buff.trick_shots.up&buff.bulletstorm.up&full_recharge_time<gcd
actions.trickshots+=/aimed_shot,if=(buff.precise_shots.down|debuff.spotters_mark.up&buff.moving_target.up)&buff.trick_shots.up&!variable.burst_phase&action.aimed_shot.charges>=1

# 爆炸射击 - 严格在瞄准射击后，利用BUG
actions.trickshots+=/explosive_shot,if=talent.precision_detonation&prev.1.aimed_shot&!variable.burst_phase
actions.trickshots+=/explosive_shot,if=prev.1.aimed_shot&!variable.burst_phase&cooldown.explosive_shot.ready&!talent.shrapnel_shot
actions.trickshots+=/explosive_shot,if=talent.precision_detonation&talent.shrapnel_shot&buff.lock_and_load.down&prev.1.aimed_shot&!variable.burst_phase

# 乱射优先级 - 避免打断瞄准→爆炸连招
actions.trickshots+=/volley,if=buff.double_tap.down&talent.volley&(variable.burst_phase|cooldown.trueshot.remains>10)&!prev_gcd.1.aimed_shot
actions.trickshots+=/volley,if=buff.double_tap.down&talent.volley&!variable.burst_phase&!prev_gcd.1.aimed_shot

# 急速射击优化 - 确保技巧射击BUFF存在，避免打断连招
actions.trickshots+=/rapid_fire,if=talent.bulletstorm&buff.bulletstorm.down&buff.trick_shots.remains>execute_time&!prev_gcd.1.aimed_shot
actions.trickshots+=/rapid_fire,if=hero_tree.sentinel&buff.lunar_storm_cooldown.down&buff.trick_shots.remains>execute_time&!prev_gcd.1.aimed_shot
actions.trickshots+=/rapid_fire,if=buff.trick_shots.remains>execute_time&!variable.burst_phase&!prev_gcd.1.aimed_shot

# 多重射击维护技巧射击BUFF - 避免打断连招
actions.trickshots+=/multishot,cycle_targets=1,if=buff.precise_shots.up&buff.moving_target.down&buff.trick_shots.up&!prev_gcd.1.aimed_shot
actions.trickshots+=/multishot,if=buff.trick_shots.up&active_enemies>=3&!prev_gcd.1.aimed_shot

# 最后的集中值管理 - 避免稳固射击，优先多重射击，避免打断连招
actions.trickshots+=/multishot,if=focus>=40&active_enemies>=3&!prev_gcd.1.aimed_shot
actions.trickshots+=/arcane_shot,if=focus>=40&buff.precise_shots.up&!prev_gcd.1.aimed_shot

# 仅在极端情况下使用稳固射击 - 避免打断连招
actions.trickshots+=/steady_shot,if=focus<variable.focus_threshold_low&!variable.burst_phase&buff.trick_shots.up&!prev_gcd.1.aimed_shot

# ========== 饰品管理 - 简化版 ==========
actions.trinkets+=/variable,name=sync_ready,value=variable.trueshot_ready
actions.trinkets+=/variable,name=sync_active,value=variable.burst_phase

# 饰品使用逻辑 - 大幅简化
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket1,if=trinket.1.has_use_buff&(variable.sync_ready&variable.trinket_1_stronger|variable.sync_active&variable.trinket_1_stronger|!trinket.2.has_use_buff&variable.trinket_1_stronger)|!trinket.1.has_use_buff&!variable.sync_active&(variable.trinket_1_stronger|trinket.2.cooldown.remains>20)|boss&fight_remains<30&variable.trinket_1_stronger

actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket2,if=trinket.2.has_use_buff&(variable.sync_ready&variable.trinket_2_stronger|variable.sync_active&variable.trinket_2_stronger|!trinket.1.has_use_buff&variable.trinket_2_stronger)|!trinket.2.has_use_buff&!variable.sync_active&(variable.trinket_2_stronger|trinket.1.cooldown.remains>20)|boss&fight_remains<30&variable.trinket_2_stronger

# ========== 版本说明 ==========

# 适用版本：魔兽世界11.2版本
# BUG利用优化版本 - 专注于瞄准射击→爆炸射击连招BUG
# 优化日期：2025-08-16
# 主要特色：
# 1. 严格的技能顺序控制，确保瞄准射击后立即释放爆炸射击
# 2. 所有其他技能都添加了!prev_gcd.1.aimed_shot条件，避免打断连招
# 3. 最大化精确引爆天赋的BUG利用效果
# 4. 修复双目标循环中无效的多重射击使用（技巧射击需要3+目标才能触发）
# 5. 双目标循环改为基于单体循环的逻辑，使用奥术射击作为填充技能
# 6. 适用于追求极限输出的高端玩家