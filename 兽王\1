hunter="哈士奇的远征"
source=default
spec=beast_mastery
level=80
race=human
role=attack
position=ranged_back
professions=leatherworking=69/engineering=27
talents=C0PA11Mk8aZ38kAf+zso3nZ9IYMbzYmZYxkNsMzWDNjNAAAAAAMmxYsMzMMjZwywMzYmZmZyMmxMzMjxMDDjhxwYY2WmZYDAAAAAAmB
shadowlands.soleahs_secret_technique_type_override=haste

# Default consumables
potion=disabled
flask=disabled
food=disabled
augmentation=disabled
temporary_enchant=disabled

# This default action priority list is automatically created based on your character.
# It is a attempt to provide you with a action list that is both simple and practicable,
# while resulting in a meaningful and good simulation. It may not result in the absolutely highest possible dps.
# Feel free to edit, adapt and improve it to your own needs.
# SimulationCraft is always looking for updates and improvements to the default action lists.

# Executed before combat begins. Accepts non-harmful actions only.
actions.precombat=summon_pet
actions.precombat+=/snapshot_stats
actions.precombat+=/variable,name=stronger_trinket_slot,op=setif,value=1,value_else=2,condition=!trinket.2.has_cooldown|trinket.1.has_use_buff&(!trinket.2.has_use_buff|trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration)|!trinket.1.has_use_buff&(!trinket.2.has_use_buff&(trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration))

# Executed every time the actor is available.
actions=auto_shot
actions+=/call_action_list,name=cds
actions+=/call_action_list,name=trinkets
actions+=/call_action_list,name=drst,if=talent.black_arrow&(active_enemies<2|!talent.beast_cleave&active_enemies<3)
actions+=/call_action_list,name=drcleave,if=talent.black_arrow&(active_enemies>2|talent.beast_cleave&active_enemies>1)
actions+=/call_action_list,name=st,if=!talent.black_arrow&(active_enemies<2|!talent.beast_cleave&active_enemies<3)
actions+=/call_action_list,name=cleave,if=!talent.black_arrow&(active_enemies>2|talent.beast_cleave&active_enemies>1)

actions.cds=invoke_external_buff,name=power_infusion,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&(buff.bestial_wrath.up|cooldown.bestial_wrath.remains<30)|fight_remains<16
actions.cds+=/berserking,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|fight_remains<13
actions.cds+=/blood_fury,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|fight_remains<16
actions.cds+=/ancestral_call,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|fight_remains<16
actions.cds+=/fireblood,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|fight_remains<9
actions.cds+=/potion,if=buff.call_of_the_wild.up|!talent.call_of_the_wild&buff.bestial_wrath.up|fight_remains<31

actions.cleave=bestial_wrath,if=buff.howl_of_the_pack_leader_cooldown.remains-buff.lead_from_the_front.duration<buff.lead_from_the_front.duration%gcd*0.5|!set_bonus.tww3_4pc|talent.multishot
actions.cleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=full_recharge_time<gcd|charges_fractional>=cooldown.kill_command.charges_fractional|talent.call_of_the_wild&cooldown.call_of_the_wild.ready|howl_summon.ready&full_recharge_time<8
actions.cleave+=/bloodshed
actions.cleave+=/multishot,if=pet.main.buff.beast_cleave.down&(!talent.bloody_frenzy|cooldown.call_of_the_wild.remains)
actions.cleave+=/call_of_the_wild
actions.cleave+=/explosive_shot,if=talent.thundering_hooves
actions.cleave+=/kill_command
actions.cleave+=/cobra_shot,if=focus.time_to_max<gcd*2|buff.hogstrider.stack>3|!talent.multishot

actions.drcleave=kill_shot
actions.drcleave+=/bestial_wrath,if=cooldown.call_of_the_wild.remains>20|!talent.call_of_the_wild
actions.drcleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=full_recharge_time<gcd
actions.drcleave+=/bloodshed
actions.drcleave+=/multishot,if=pet.main.buff.beast_cleave.down&(!talent.bloody_frenzy|cooldown.call_of_the_wild.remains)
actions.drcleave+=/call_of_the_wild
actions.drcleave+=/explosive_shot,if=talent.thundering_hooves
actions.drcleave+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=charges_fractional>=cooldown.kill_command.charges_fractional
actions.drcleave+=/kill_command
actions.drcleave+=/cobra_shot,if=focus.time_to_max<gcd*2
actions.drcleave+=/explosive_shot

actions.drst=kill_shot
actions.drst+=/bestial_wrath,if=cooldown.call_of_the_wild.remains>20|!talent.call_of_the_wild
actions.drst+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=full_recharge_time<gcd
actions.drst+=/bloodshed
actions.drst+=/call_of_the_wild
actions.drst+=/kill_command
actions.drst+=/barbed_shot,target_if=min:dot.barbed_shot.remains
actions.drst+=/cobra_shot

actions.st=bestial_wrath,if=buff.howl_of_the_pack_leader_cooldown.remains-buff.lead_from_the_front.duration<buff.lead_from_the_front.duration%gcd*0.5|!set_bonus.tww3_4pc
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains,if=full_recharge_time<gcd
actions.st+=/call_of_the_wild
actions.st+=/bloodshed
actions.st+=/kill_command,if=charges_fractional>=cooldown.barbed_shot.charges_fractional&!(buff.lead_from_the_front.remains>gcd&buff.lead_from_the_front.remains<gcd*2&!howl_summon.ready&full_recharge_time>gcd)
actions.st+=/barbed_shot,target_if=min:dot.barbed_shot.remains
actions.st+=/cobra_shot

actions.trinkets=variable,name=buff_sync_ready,value=talent.call_of_the_wild&(prev_gcd.1.call_of_the_wild)|talent.bloodshed&(prev_gcd.1.bloodshed)|(!talent.call_of_the_wild&!talent.bloodshed)&(buff.bestial_wrath.up|cooldown.bestial_wrath.remains_guess<5)
actions.trinkets+=/variable,name=buff_sync_remains,op=setif,value=cooldown.bestial_wrath.remains_guess,value_else=cooldown.call_of_the_wild.remains|cooldown.bloodshed.remains,condition=!talent.call_of_the_wild&!talent.bloodshed
actions.trinkets+=/variable,name=buff_sync_active,value=talent.call_of_the_wild&buff.call_of_the_wild.up|talent.bloodshed&prev_gcd.1.bloodshed|(!talent.call_of_the_wild&!talent.bloodshed)&buff.bestial_wrath.up
actions.trinkets+=/variable,name=damage_sync_active,value=1
actions.trinkets+=/variable,name=damage_sync_remains,value=0
actions.trinkets+=/use_items,slots=trinket1:trinket2,if=this_trinket.has_use_buff&(variable.buff_sync_ready&(variable.stronger_trinket_slot=this_trinket_slot|other_trinket.cooldown.remains)|!variable.buff_sync_ready&(variable.stronger_trinket_slot=this_trinket_slot&(variable.buff_sync_remains>this_trinket.cooldown.duration%3&fight_remains>this_trinket.cooldown.duration+20|other_trinket.has_use_buff&other_trinket.cooldown.remains>variable.buff_sync_remains-15&other_trinket.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains+45>fight_remains)|variable.stronger_trinket_slot!=this_trinket_slot&(other_trinket.cooldown.remains&(other_trinket.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains>=20|other_trinket.cooldown.remains-5>=variable.buff_sync_remains&(variable.buff_sync_remains>this_trinket.cooldown.duration%3|this_trinket.cooldown.duration<fight_remains&(variable.buff_sync_remains+this_trinket.cooldown.duration>fight_remains)))|other_trinket.cooldown.ready&variable.buff_sync_remains>20&variable.buff_sync_remains<other_trinket.cooldown.duration%3)))|!this_trinket.has_use_buff&(this_trinket.cast_time=0|!variable.buff_sync_active)&(!this_trinket.is.junkmaestros_mega_magnet|buff.junkmaestros_mega_magnet.stack>10)&(!other_trinket.has_cooldown&(variable.damage_sync_active|this_trinket.is.junkmaestros_mega_magnet&buff.junkmaestros_mega_magnet.stack>25|!this_trinket.is.junkmaestros_mega_magnet&variable.damage_sync_remains>this_trinket.cooldown.duration%3)|other_trinket.has_cooldown&(!other_trinket.has_use_buff&(variable.stronger_trinket_slot=this_trinket_slot|other_trinket.cooldown.remains)&(variable.damage_sync_active|this_trinket.is.junkmaestros_mega_magnet&buff.junkmaestros_mega_magnet.stack>25|variable.damage_sync_remains>this_trinket.cooldown.duration%3&!this_trinket.is.junkmaestros_mega_magnet|other_trinket.cooldown.remains-5<variable.damage_sync_remains&variable.damage_sync_remains>=20)|other_trinket.has_use_buff&(variable.damage_sync_active|this_trinket.is.junkmaestros_mega_magnet&buff.junkmaestros_mega_magnet.stack>25|!this_trinket.is.junkmaestros_mega_magnet&variable.damage_sync_remains>this_trinket.cooldown.duration%3)&(other_trinket.cooldown.remains>=20|other_trinket.cooldown.remains-5>variable.buff_sync_remains)))|fight_remains<25&(variable.stronger_trinket_slot=this_trinket_slot|other_trinket.cooldown.remains)

head=midnight_heralds_cowl,id=237646,bonus_id=6652/12921/10390/12231/12353/1514/10255/12676
neck=duskblazes_desperation,id=237569,bonus_id=6652/10354/10879/10396/12297/1514/10255,gem_id=213455/213455
shoulders=midnight_heralds_shadowguards,id=237644,bonus_id=6652/10390/12233/12675/12353/1514/10255
back=reshii_wraps,id=235499,bonus_id=12401/9893,gem_id=238045,enchant_id=7409
chest=midnight_heralds_hauberk,id=237649,bonus_id=10354/12229/6652/12676/12297/1514/10255,enchant_id=7364
shirt=lucky_shirt,id=138385
tabard=renowned_guild_tabard,id=69210
wrists=pactbound_vambraces,id=237555,bonus_id=6652/12921/12239/10355/12353/1514/10255,enchant_id=7397
hands=gloves_of_whispering_winds,id=156317,bonus_id=7756/12239/10383/12296/11406/10255
waist=durable_information_securing_container,id=245965,bonus_id=12533/1489,gem_id=213455,titan_disc_id=1236275
legs=midnight_heralds_petticoat,id=237645,bonus_id=10354/12232/6652/12676/12297/1514/10255,enchant_id=7595
feet=boots_of_unsettled_prey,id=156467,bonus_id=8902/7756/12239/10383/12352/11406/10255,enchant_id=7418
finger1=whispers_of_karesh,id=242491,bonus_id=10390/6652/10383/10879/10396/12296/3193/10255,gem_id=213455/213455,enchant_id=7334
finger2=devout_zealots_ring,id=221136,bonus_id=12352/10390/6652/10383/3193/10255/10879/10396,gem_id=213455/213455,enchant_id=7334
trinket1=lily_of_the_eternal_weave,id=242494,bonus_id=10390/6652/10383/12353/3196/10255
trinket2=improvised_seaforium_pacemaker,id=232541,bonus_id=10390/6652/10383/12376/1520/10255
main_hand=p.0.w._x2,id=221969,bonus_id=10421/9633/8902/9627/12050/8790/10518/8960/12053,enchant_id=7439,crafted_stats=40/49

# Gear Summary
# gear_ilvl=704.47
# gear_agility=72023
# gear_stamina=553998
# gear_crit_rating=16261
# gear_haste_rating=23269
# gear_mastery_rating=8037
# gear_versatility_rating=625
# gear_leech_rating=1020
# gear_speed_rating=1250
# gear_armor=55905
# set_bonus=name=thewarwithin_season_3,pc=2,hero_tree=pack_leader,enable=1
# set_bonus=name=thewarwithin_season_3,pc=4,hero_tree=pack_leader,enable=1