## Date: 2025-08-15
## 射击猎人手法 - 简化实用版 v3.1

# ========== 预战斗设置 ==========
actions.precombat+=/summon_pet,if=talent.unbreakable_bond

# 饰品优先级判断 - 进一步简化
actions.precombat+=/variable,name=trinket_1_stronger,value=trinket.1.has_use_buff&!trinket.2.has_use_buff|trinket.1.has_use_buff&trinket.2.has_use_buff&trinket.1.cooldown.duration>=trinket.2.cooldown.duration|!trinket.1.has_use_buff&!trinket.2.has_use_buff&trinket.1.cooldown.duration>=trinket.2.cooldown.duration
actions.precombat+=/variable,name=trinket_2_stronger,value=!variable.trinket_1_stronger

# 核心变量定义 - 统一管理
actions.precombat+=/variable,name=pet_healing_threshold,value=40
actions.precombat+=/variable,name=aimed_shot_base_cost,value=35
actions.precombat+=/variable,name=focus_threshold_high,value=60
actions.precombat+=/variable,name=focus_threshold_low,value=30
actions.precombat+=/variable,name=execute_threshold,value=20

# 完美射击窗口定义 - 多BUFF同步
actions.precombat+=/variable,name=perfect_shot_window,value=buff.trueshot.up&buff.bulletstorm.up&(buff.lunar_storm.up|!hero_tree.sentinel)

# 爆发技能准备状态
actions.precombat+=/variable,name=burst_skills_ready,value=cooldown.trueshot.ready&(cooldown.explosive_shot.ready|!talent.explosive_shot)&(cooldown.black_arrow.ready|!talent.black_arrow)

actions.precombat+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20

# 预战斗技能 - 根据天赋优化
actions.precombat+=/aimed_shot,if=active_enemies<3|talent.black_arrow&talent.headshot
actions.precombat+=/steady_shot

# ========== 主循环 ==========
# 动态变量更新 - 实时计算资源状态
actions+=/variable,name=aimed_shot_focus_cost,value=variable.aimed_shot_base_cost*(1-0.5*buff.trueshot.up)
actions+=/variable,name=burst_phase,value=buff.trueshot.up|(buff.lunar_storm.up&hero_tree.sentinel)
actions+=/variable,name=focus_ok,value=focus>=variable.aimed_shot_focus_cost
actions+=/variable,name=execute_phase,value=target.health.pct<variable.execute_threshold
actions+=/variable,name=trueshot_ready,value=cooldown.trueshot.ready&(!talent.bullseye|fight_remains>25|buff.bullseye.stack=buff.bullseye.max_stack)&(!trinket.1.has_use_buff|trinket.1.cooldown.remains>30|trinket.1.cooldown.ready)&(!trinket.2.has_use_buff|trinket.2.cooldown.remains>30|trinket.2.cooldown.ready)|boss&fight_remains<25

# 优化爆发时机判断
actions+=/variable,name=should_burst,value=variable.trueshot_ready&variable.burst_skills_ready&(target.time_to_die>15|variable.execute_phase)

# 打断和驱散
actions+=/counter_shot
actions+=/tranquilizing_shot

# 生存技能 - 优化触发条件
actions+=/mend_pet,if=pet.health_pct<variable.pet_healing_threshold
actions+=/exhilaration,if=health.pct<40&!variable.burst_phase
actions+=/aspect_of_the_cheetah,if=!in_combat&!buff.aspect_of_the_cheetah.up

# 猎人印记维护
actions+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20

# 循环优先级 - 严格目标数量判断
actions+=/call_action_list,name=burst,if=variable.burst_phase|variable.trueshot_ready
actions+=/call_action_list,name=cds
actions+=/call_action_list,name=trinkets
actions+=/call_action_list,name=trickshots,strict=1,if=active_enemies>=3&talent.trick_shots
actions+=/call_action_list,name=cleave,strict=1,if=active_enemies=2|active_enemies>2&!talent.trick_shots
actions+=/call_action_list,name=st,if=active_enemies=1

# ========== 冷却技能管理 ==========
# 种族技能 - 简化条件判断
actions.cds+=/berserking,if=variable.burst_phase|boss&fight_remains<15
actions.cds+=/blood_fury,if=variable.burst_phase|cooldown.trueshot.remains>30|boss&fight_remains<20
actions.cds+=/ancestral_call,if=variable.burst_phase|cooldown.trueshot.remains>30|boss&fight_remains<20
actions.cds+=/fireblood,if=variable.burst_phase|cooldown.trueshot.remains>30|boss&fight_remains<15
actions.cds+=/lights_judgment,if=!variable.burst_phase

# 药水使用 - 优化同步时机
actions.cds+=/potion,if=variable.burst_phase&(buff.bloodlust.up|target.health.pct<20)|boss&fight_remains<35

# ========== 爆发循环 - 简化实用版 ==========
# 单体爆发 - 简化条件，提升稳定性
actions.burst+=/trueshot,if=active_enemies=1&variable.trueshot_ready&!buff.trueshot.up
actions.burst+=/kill_shot,if=active_enemies=1&variable.burst_phase&(target.health.pct<20|buff.razor_fragments.up|talent.headshot&buff.precise_shots.up)
actions.burst+=/black_arrow,if=active_enemies=1&variable.burst_phase&buff.precise_shots.stack>=2&cooldown.black_arrow.ready
actions.burst+=/aimed_shot,if=active_enemies=1&variable.burst_phase&action.aimed_shot.charges>=1&focus>=35&(buff.streamline.stack>=1|buff.trueshot.up)
actions.burst+=/explosive_shot,if=active_enemies=1&variable.burst_phase&cooldown.explosive_shot.ready
actions.burst+=/rapid_fire,if=active_enemies=1&variable.burst_phase&(buff.bulletstorm.remains<3|!buff.bulletstorm.up)
actions.burst+=/volley,if=active_enemies=1&variable.burst_phase&talent.volley&cooldown.volley.ready
actions.burst+=/arcane_shot,if=active_enemies=1&variable.burst_phase&buff.precise_shots.up&focus>=40
actions.burst+=/arcane_shot,if=active_enemies=1&variable.burst_phase&focus>=40&action.aimed_shot.charges<1

# 双目标爆发 - 简化版本
actions.burst+=/multishot,if=active_enemies=2&!buff.trick_shots.up
actions.burst+=/trueshot,if=active_enemies=2&variable.trueshot_ready&!buff.trueshot.up
actions.burst+=/aimed_shot,if=active_enemies=2&variable.burst_phase&action.aimed_shot.charges>=1&focus>=35
actions.burst+=/explosive_shot,if=active_enemies=2&variable.burst_phase&cooldown.explosive_shot.ready
actions.burst+=/volley,if=active_enemies=2&variable.burst_phase&talent.volley&cooldown.volley.ready
actions.burst+=/rapid_fire,if=active_enemies=2&variable.burst_phase&(buff.bulletstorm.remains<3|!buff.bulletstorm.up)
actions.burst+=/multishot,if=active_enemies=2&variable.burst_phase&focus>=40

# AOE爆发循环 - 简化版本
actions.burst+=/multishot,if=active_enemies>=3&!buff.trick_shots.up
actions.burst+=/trueshot,if=active_enemies>=3&variable.trueshot_ready&!buff.trueshot.up&(buff.trick_shots.up|!talent.trick_shots)
actions.burst+=/aimed_shot,if=active_enemies>=3&variable.burst_phase&(buff.trick_shots.up|!talent.trick_shots)&action.aimed_shot.charges>=1&focus>=35
actions.burst+=/explosive_shot,if=active_enemies>=3&variable.burst_phase&(buff.trick_shots.up|!talent.trick_shots)&cooldown.explosive_shot.ready
actions.burst+=/rapid_fire,if=active_enemies>=3&variable.burst_phase&(buff.trick_shots.up|!talent.trick_shots)&!buff.bulletstorm.up
actions.burst+=/volley,if=active_enemies>=3&variable.burst_phase&(buff.trick_shots.up|!talent.trick_shots)&talent.volley&cooldown.volley.ready
actions.burst+=/multishot,if=active_enemies>=3&variable.burst_phase&(buff.trick_shots.up|!talent.trick_shots)&focus>=40

# ========== 双目标循环 - 简化实用版 ==========
# 开场技能 - 确保技巧射击BUFF
actions.cleave+=/multishot,if=!buff.trick_shots.up&active_enemies>=2
actions.cleave+=/volley,if=talent.volley&active_enemies>=2&cooldown.volley.ready

# 黑蚀箭优先级 - 简化条件
actions.cleave+=/black_arrow,if=buff.precise_shots.stack>=2&cooldown.black_arrow.ready

# 百发百中开启
actions.cleave+=/trueshot,if=variable.trueshot_ready&!variable.burst_phase

# 瞄准射击优先级 - 简化条件
actions.cleave+=/aimed_shot,cycle_targets=1,if=action.aimed_shot.charges>=1&focus>=35&(buff.streamline.stack>=1|!variable.burst_phase)
actions.cleave+=/aimed_shot,cycle_targets=1,if=action.aimed_shot.charges>=1&focus>=35&buff.trueshot.up

# 爆炸射击 - 简化使用条件
actions.cleave+=/explosive_shot,if=cooldown.explosive_shot.ready&!variable.burst_phase

# 乱射优先级
actions.cleave+=/volley,if=active_enemies>=2&talent.volley&cooldown.volley.ready&!variable.burst_phase

# 急速射击优化
actions.cleave+=/rapid_fire,if=talent.bulletstorm&buff.bulletstorm.down&!variable.burst_phase
actions.cleave+=/rapid_fire,if=talent.lunar_storm&buff.lunar_storm_cooldown.down&hero_tree.sentinel
actions.cleave+=/rapid_fire,if=!variable.burst_phase&(buff.bulletstorm.remains<3|!buff.bulletstorm.up)

# 夺命射击
actions.cleave+=/kill_shot,cycle_targets=1,if=target.health.pct<20|buff.razor_fragments.up|(talent.headshot&buff.precise_shots.up)

# 填充技能 - 简化版本
actions.cleave+=/multishot,cycle_targets=1,if=active_enemies>=2&buff.precise_shots.up&focus>=40
actions.cleave+=/arcane_shot,cycle_targets=1,if=buff.precise_shots.up&focus>=40
actions.cleave+=/multishot,if=active_enemies>=2&focus>=40

# 集中值管理 - 最后选择
actions.cleave+=/steady_shot,if=focus<30&!variable.burst_phase

# ========== 单体循环 - 简化实用版 ==========
# 夺命射击最高优先级 - 斩杀阶段和BUFF期
actions.st+=/kill_shot,if=target.health.pct<20|buff.razor_fragments.up|(talent.headshot&buff.precise_shots.up)

# 黑暗游侠天赋优先级 - 黑蚀箭配合弹无虚发
actions.st+=/black_arrow,if=buff.precise_shots.stack>=2&cooldown.black_arrow.ready

# 百发百中开启
actions.st+=/trueshot,if=variable.trueshot_ready

# 哨兵天赋月之风暴优化
actions.st+=/rapid_fire,if=talent.lunar_storm&buff.lunar_storm_cooldown.down&hero_tree.sentinel&action.aimed_shot.charges>=1
actions.st+=/aimed_shot,if=hero_tree.sentinel&buff.lunar_storm.up&focus>=35&(buff.streamline.stack>=1|buff.trueshot.up)

# 爆炸射击 - 简化使用条件
actions.st+=/explosive_shot,if=cooldown.explosive_shot.ready&!variable.burst_phase

# 黑蚀箭常规使用
actions.st+=/black_arrow,if=buff.precise_shots.up&cooldown.black_arrow.ready

# 瞄准射击优先级 - 简化条件
actions.st+=/aimed_shot,if=buff.trueshot.up&focus>=35&(buff.streamline.stack>=1|action.aimed_shot.charges>=2)
actions.st+=/aimed_shot,if=!variable.burst_phase&focus>=35&buff.streamline.stack>=1
actions.st+=/aimed_shot,if=action.aimed_shot.charges>=2&focus>=35

# 急速射击优化
actions.st+=/rapid_fire,if=(!variable.burst_phase|focus<50)&(buff.bulletstorm.remains<3|!buff.bulletstorm.up)

# 填充技能优化
actions.st+=/arcane_shot,if=buff.precise_shots.up&focus>=40&(!talent.black_arrow|cooldown.black_arrow.remains>3)
actions.st+=/explosive_shot,if=hero_tree.sentinel&buff.lunar_storm.up&cooldown.explosive_shot.ready

# 集中值管理 - 简化版本
actions.st+=/steady_shot,if=focus<35&action.aimed_shot.charges>=1&!variable.burst_phase
actions.st+=/steady_shot,if=focus<60&!variable.burst_phase&action.aimed_shot.charges<1

# ========== 多目标循环 - 简化实用版 ==========
# 开场优先级 - 确保技巧射击BUFF
actions.trickshots+=/multishot,cycle_targets=1,if=buff.trick_shots.down|buff.trick_shots.remains<gcd.max
actions.trickshots+=/multishot,cycle_targets=1,if=!buff.trick_shots.up&active_enemies>=3

# 百发百中开启
actions.trickshots+=/trueshot,if=variable.trueshot_ready&buff.trick_shots.up

# 黑蚀箭使用 - 替代多重射击
actions.trickshots+=/black_arrow,if=buff.precise_shots.up&cooldown.black_arrow.ready

# 瞄准射击优先级 - 简化条件
actions.trickshots+=/aimed_shot,if=buff.trick_shots.up&action.aimed_shot.charges>=1&focus>=35&(buff.streamline.stack>=1|buff.trueshot.up)

# 爆炸射击 - 简化使用条件
actions.trickshots+=/explosive_shot,if=cooldown.explosive_shot.ready&!variable.burst_phase&buff.trick_shots.up

# 乱射优先级
actions.trickshots+=/volley,if=talent.volley&cooldown.volley.ready&!variable.burst_phase&buff.trick_shots.up

# 急速射击优化
actions.trickshots+=/rapid_fire,if=talent.bulletstorm&buff.bulletstorm.down&buff.trick_shots.remains>execute_time
actions.trickshots+=/rapid_fire,if=hero_tree.sentinel&buff.lunar_storm_cooldown.down&buff.trick_shots.remains>execute_time
actions.trickshots+=/rapid_fire,if=buff.trick_shots.remains>execute_time&!variable.burst_phase

# 多重射击维护技巧射击BUFF
actions.trickshots+=/multishot,cycle_targets=1,if=buff.precise_shots.up&buff.trick_shots.up&focus>=40
actions.trickshots+=/multishot,if=buff.trick_shots.up&active_enemies>=3&focus>=40

# 集中值管理 - 简化版本
actions.trickshots+=/multishot,if=focus>=40&active_enemies>=3
actions.trickshots+=/arcane_shot,if=focus>=40&buff.precise_shots.up

# 最后选择稳固射击
actions.trickshots+=/steady_shot,if=focus<30&!variable.burst_phase&buff.trick_shots.up

# ========== 饰品管理 - 简化版 ==========
actions.trinkets+=/variable,name=sync_ready,value=variable.trueshot_ready
actions.trinkets+=/variable,name=sync_active,value=variable.burst_phase

# 饰品使用逻辑 - 大幅简化
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket1,if=trinket.1.has_use_buff&(variable.sync_ready&variable.trinket_1_stronger|variable.sync_active&variable.trinket_1_stronger|!trinket.2.has_use_buff&variable.trinket_1_stronger)|!trinket.1.has_use_buff&!variable.sync_active&(variable.trinket_1_stronger|trinket.2.cooldown.remains>20)|boss&fight_remains<30&variable.trinket_1_stronger

actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket2,if=trinket.2.has_use_buff&(variable.sync_ready&variable.trinket_2_stronger|variable.sync_active&variable.trinket_2_stronger|!trinket.1.has_use_buff&variable.trinket_2_stronger)|!trinket.2.has_use_buff&!variable.sync_active&(variable.trinket_2_stronger|trinket.1.cooldown.remains>20)|boss&fight_remains<30&variable.trinket_2_stronger

# ========== 版本说明 ==========
# 版本：v3.1 简化实用版

# 适用版本：魔兽世界11.2版本