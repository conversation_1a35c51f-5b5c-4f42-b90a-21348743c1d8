hunter="哈士奇的远征"
source=default
spec=survival
level=80
race=human
role=attack
position=back
professions=leatherworking=69/engineering=27
talents=C8PA11Mk8aZ38kAf+zso3nZ9IMGYglxoxyAysFsNzYZmZmZmhxMzMjxYMzYWAAAAAAAaGzYGzMDzwMMGmZYMMLLzgNAAAAAYAA
shadowlands.soleahs_secret_technique_type_override=haste

# 默认消耗品
potion=disabled
flask=disabled
food=disabled
augmentation=disabled
temporary_enchant=disabled

# 战前准备 - 兽王2.8风格的简洁设计
actions.precombat=summon_pet
actions.precombat+=/snapshot_stats
actions.precombat+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20

# 简单有效的变量
actions.precombat+=/variable,name=lunar_storm_window,value=0

# 兽王2.8风格的饰品强度判断
actions.precombat+=/variable,name=trinket_1_stronger,value=!trinket.2.has_cooldown|trinket.1.has_use_buff&(!trinket.2.has_use_buff|!trinket.1.is.mirror_of_fractured_tomorrows&(trinket.2.is.mirror_of_fractured_tomorrows|trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration))|!trinket.1.has_use_buff&(!trinket.2.has_use_buff&(trinket.2.cooldown.duration<trinket.1.cooldown.duration|trinket.2.cast_time<trinket.1.cast_time|trinket.2.cast_time=trinket.1.cast_time&trinket.2.cooldown.duration=trinket.1.cooldown.duration))
actions.precombat+=/variable,name=trinket_2_stronger,value=!variable.trinket_1_stronger

# 主循环 - 兽王2.8风格：简洁高效
actions=auto_attack
# 简单的月之风暴窗口检测
actions+=/variable,name=lunar_storm_window,value=buff.lunar_storm.up|buff.lunar_storm_cooldown.remains<3

actions+=/call_action_list,name=cds
actions+=/call_action_list,name=trinkets
actions+=/call_action_list,name=plst,if=active_enemies<3&talent.howl_of_the_pack_leader
actions+=/call_action_list,name=plcleave,if=active_enemies>2&talent.howl_of_the_pack_leader
actions+=/call_action_list,name=sentst,if=active_enemies<3&!talent.howl_of_the_pack_leader
actions+=/call_action_list,name=sentcleave,if=active_enemies>2&!talent.howl_of_the_pack_leader
# 实战工具与保命技能 - 兽王2.8融合风格
actions+=/counter_shot
actions+=/tranquilizing_shot
actions+=/exhilaration,if=health.pct<=50
actions+=/survival_of_the_fittest,if=health.pct<=40
actions+=/aspect_of_the_turtle,if=health.pct<=15
actions+=/mend_pet,if=pet.health_pct<pet_healing
actions+=/feign_death,if=debuff.dispellable_poison.up|debuff.dispellable_disease.up|debuff.dispellable_curse.up
actions+=/hunters_mark,if=(settings.mark_any|target.is_boss)&active_dot.hunters_mark=0&target.time_to_pct_80>20

# 种族技能
actions+=/arcane_torrent
actions+=/bag_of_tricks
actions+=/lights_judgment

# 爆发技能管理
actions.cds=blood_fury,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/invoke_external_buff,name=power_infusion,if=(buff.coordinated_assault.up&buff.coordinated_assault.remains>7&!buff.power_infusion.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault)
actions.cds+=/harpoon,if=prev.kill_command
actions.cds+=/ancestral_call,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/fireblood,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
actions.cds+=/berserking,if=buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault|time_to_die<13
actions.cds+=/muzzle
actions.cds+=/potion,if=target.time_to_die<25|buff.coordinated_assault.up|!talent.coordinated_assault&cooldown.spearhead.remains|!talent.spearhead&!talent.coordinated_assault
# 旧的简化饰品调用已注释，统一使用actions.trinkets管理
actions.cds+=/aspect_of_the_eagle,if=target.distance>=6
actions.cds+=/use_item,name=spellstrike_warplance

# 猎群领袖 | 多目标循环
actions.plcleave=spearhead,if=cooldown.coordinated_assault.remains
actions.plcleave+=/kill_command,if=buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1
actions.plcleave+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/wildfire_bomb,if=cooldown.wildfire_bomb.charges_fractional>1.7
actions.plcleave+=/explosive_shot,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/raptor_bite,target_if=max:dot.serpent_sting.remains,if=buff.strike_it_rich.up&buff.strike_it_rich.remains<gcd|buff.hogstrider.remains&boar_charge.remains>0|buff.hogstrider.remains<gcd&buff.hogstrider.up|buff.hogstrider.remains&buff.strike_it_rich.remains|raid_event.adds.exists&raid_event.adds.remains<4
actions.plcleave+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/kill_command,if=(buff.howl_of_the_pack_leader_wyvern.remains|buff.howl_of_the_pack_leader_boar.remains|buff.howl_of_the_pack_leader_bear.remains)
actions.plcleave+=/flanking_strike,if=buff.tip_of_the_spear.stack>0
actions.plcleave+=/butchery,if=cooldown.wildfire_bomb.charges_fractional<1.5
actions.plcleave+=/coordinated_assault,if=buff.howl_of_the_pack_leader_cooldown.remains-buff.lead_from_the_front.duration<buff.lead_from_the_front.duration%gcd*0.6
actions.plcleave+=/kill_command,if=focus+cast_regen<focus.max
actions.plcleave+=/explosive_shot
actions.plcleave+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!talent.contagious_reagents
actions.plcleave+=/raptor_bite,target_if=max:dot.serpent_sting.remains

# 猎群领袖 | 单体循环
actions.plst=kill_command,if=(buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1)|(buff.howl_of_the_pack_leader_wyvern.remains|buff.howl_of_the_pack_leader_boar.remains|buff.howl_of_the_pack_leader_bear.remains)
actions.plst+=/spearhead,if=cooldown.coordinated_assault.remains
actions.plst+=/flanking_strike,if=buff.tip_of_the_spear.stack>0&(cooldown.spearhead.remains>5|!talent.spearhead&cooldown.coordinated_assault.remains>5)
actions.plst+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!dot.serpent_sting.ticking&target.time_to_die>12&(!talent.contagious_reagents|active_dot.serpent_sting=0)
actions.plst+=/raptor_bite,target_if=max:dot.serpent_sting.remains,if=talent.contagious_reagents&active_dot.serpent_sting<active_enemies&dot.serpent_sting.remains
actions.plst+=/kill_command,if=buff.strike_it_rich.remains&buff.tip_of_the_spear.stack<1
actions.plst+=/raptor_bite,if=buff.strike_it_rich.remains&buff.tip_of_the_spear.stack>0
actions.plst+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0&(!raid_event.adds.exists|raid_event.adds.exists&raid_event.adds.in>40)
actions.plst+=/coordinated_assault,if=buff.howl_of_the_pack_leader_cooldown.remains-buff.lead_from_the_front.duration<buff.lead_from_the_front.duration%gcd*0.6|time_to_die<20|!talent.spearhead
actions.plst+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.plst+=/raptor_bite,target_if=max:dot.serpent_sting.remains,if=buff.howl_of_the_pack_leader_cooldown.up&buff.howl_of_the_pack_leader_cooldown.remains<2*gcd
actions.plst+=/kill_command,if=focus+cast_regen<focus.max&(!buff.relentless_primal_ferocity.up|(buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<2|focus<30))
actions.plst+=/explosive_shot,if=active_enemies>1
actions.plst+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!talent.contagious_reagents
actions.plst+=/raptor_bite,target_if=max:dot.serpent_sting.remains
actions.plst+=/explosive_shot

# SENTINEL | DEFAULT AOE ACTIONLIST
# 哨兵 | 多目标循环 - 兽王2.8风格：清晰优先级与可靠填充
# 高优先级：月之风暴窗口爆发
actions.sentcleave=wildfire_bomb,if=variable.lunar_storm_window&buff.tip_of_the_spear.stack>0
actions.sentcleave+=/fury_of_the_eagle,if=variable.lunar_storm_window&buff.tip_of_the_spear.stack>0

# 核心多目标循环
actions.sentcleave+=/wildfire_bomb,if=!buff.lunar_storm_cooldown.remains
actions.sentcleave+=/kill_command,if=buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1
actions.sentcleave+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0|cooldown.wildfire_bomb.charges_fractional>1.9|(talent.bombardier&cooldown.coordinated_assault.remains<2*gcd)|talent.butchery&cooldown.butchery.remains<gcd
actions.sentcleave+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0
actions.sentcleave+=/raptor_bite,target_if=max:dot.serpent_sting.remains,if=buff.strike_it_rich.up&buff.strike_it_rich.remains<gcd
actions.sentcleave+=/butchery
actions.sentcleave+=/explosive_shot,if=buff.tip_of_the_spear.stack>0
actions.sentcleave+=/coordinated_assault
actions.sentcleave+=/flanking_strike,if=buff.tip_of_the_spear.stack>0

# 集中值管理与填充
actions.sentcleave+=/kill_command,if=focus+cast_regen<focus.max
actions.sentcleave+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.sentcleave+=/explosive_shot
actions.sentcleave+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!talent.contagious_reagents
actions.sentcleave+=/raptor_bite,target_if=max:dot.serpent_sting.remains

# 永远有技能可打 - 多目标填充
actions.sentcleave+=/kill_command
actions.sentcleave+=/butchery
actions.sentcleave+=/raptor_bite

# 哨兵 | 单体循环 - 兽王2.8风格：永远有技能可打
# 高优先级：月之风暴窗口爆发
actions.sentst=wildfire_bomb,if=variable.lunar_storm_window&buff.tip_of_the_spear.stack>0
actions.sentst+=/fury_of_the_eagle,if=variable.lunar_storm_window&buff.tip_of_the_spear.stack>0

# 核心循环：维持矛尖叠层和关键技能
actions.sentst+=/wildfire_bomb,if=!buff.lunar_storm_cooldown.remains&buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,if=buff.relentless_primal_ferocity.up&buff.tip_of_the_spear.stack<1
actions.sentst+=/spearhead,if=cooldown.coordinated_assault.remains
actions.sentst+=/flanking_strike,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,if=buff.strike_it_rich.remains&buff.tip_of_the_spear.stack<1
actions.sentst+=/mongoose_bite,if=buff.strike_it_rich.remains&buff.coordinated_assault.up
actions.sentst+=/wildfire_bomb,if=cooldown.wildfire_bomb.charges_fractional>1.7
actions.sentst+=/butchery
actions.sentst+=/coordinated_assault,if=!talent.bombardier|talent.bombardier&cooldown.wildfire_bomb.charges_fractional<2
actions.sentst+=/fury_of_the_eagle,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/kill_command,if=buff.tip_of_the_spear.stack<1&cooldown.flanking_strike.remains<gcd

# 集中值管理：防止溢出和饿死
actions.sentst+=/kill_command,if=focus+cast_regen<focus.max&(!buff.relentless_primal_ferocity.up|(buff.relentless_primal_ferocity.up&(buff.tip_of_the_spear.stack<1|focus<30)))
actions.sentst+=/mongoose_bite,if=buff.mongoose_fury.remains<gcd&buff.mongoose_fury.stack>0
actions.sentst+=/wildfire_bomb,if=buff.tip_of_the_spear.stack>0
actions.sentst+=/explosive_shot
actions.sentst+=/mongoose_bite,if=buff.mongoose_fury.remains
actions.sentst+=/raptor_bite,target_if=min:dot.serpent_sting.remains,if=!talent.contagious_reagents
actions.sentst+=/raptor_bite,target_if=max:dot.serpent_sting.remains

# 永远有技能可打 - 核心填充
actions.sentst+=/kill_command
actions.sentst+=/raptor_bite

# 哨兵增强饰品同步 - 兽王2.8基础 + 月之风暴
actions.trinkets=variable,name=buff_sync_ready,value=buff.coordinated_assault.up|cooldown.coordinated_assault.remains<5|variable.lunar_storm_window
actions.trinkets+=/variable,name=buff_sync_remains,value=cooldown.coordinated_assault.remains
actions.trinkets+=/variable,name=buff_sync_active,value=buff.coordinated_assault.up|variable.lunar_storm_window
actions.trinkets+=/variable,name=damage_sync_active,value=1
actions.trinkets+=/variable,name=damage_sync_remains,value=0
# 兽王2.8风格的分槽位饰品使用
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket1,if=trinket.1.has_use_buff&(variable.buff_sync_ready&(variable.trinket_1_stronger|trinket.2.cooldown.remains)|!variable.buff_sync_ready&(variable.trinket_1_stronger&(variable.buff_sync_remains>trinket.1.cooldown.duration%3&boss&fight_remains>trinket.1.cooldown.duration+20|trinket.2.has_use_buff&trinket.2.cooldown.remains>variable.buff_sync_remains-15&trinket.2.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains+45>fight_remains&boss)|variable.trinket_2_stronger&(trinket.2.cooldown.remains&(trinket.2.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains>=20|trinket.2.cooldown.remains-5>=variable.buff_sync_remains&(variable.buff_sync_remains>trinket.1.cooldown.duration%3|trinket.1.cooldown.duration<fight_remains&boss&(variable.buff_sync_remains+trinket.1.cooldown.duration>fight_remains)))|trinket.2.cooldown.ready&variable.buff_sync_remains>20&variable.buff_sync_remains<trinket.2.cooldown.duration%3)))|!trinket.1.has_use_buff&(trinket.1.cast_time=0|!variable.buff_sync_active)&(!trinket.2.has_use_buff&(variable.trinket_1_stronger|trinket.2.cooldown.remains)|trinket.2.has_use_buff&(!variable.buff_sync_active&variable.buff_sync_remains>20|trinket.2.cooldown.remains>20))|boss&fight_remains<25&(variable.trinket_1_stronger|trinket.2.cooldown.remains)
actions.trinkets+=/use_item,use_off_gcd=1,slot=trinket2,if=trinket.2.has_use_buff&(variable.buff_sync_ready&(variable.trinket_2_stronger|trinket.1.cooldown.remains)|!variable.buff_sync_ready&(variable.trinket_2_stronger&(variable.buff_sync_remains>trinket.2.cooldown.duration%3&boss&fight_remains>trinket.2.cooldown.duration+20|trinket.1.has_use_buff&trinket.1.cooldown.remains>variable.buff_sync_remains-15&trinket.1.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains+45>fight_remains&boss)|variable.trinket_1_stronger&(trinket.1.cooldown.remains&(trinket.1.cooldown.remains-5<variable.buff_sync_remains&variable.buff_sync_remains>=20|trinket.1.cooldown.remains-5>=variable.buff_sync_remains&(variable.buff_sync_remains>trinket.2.cooldown.duration%3|trinket.2.cooldown.duration<fight_remains&boss&(variable.buff_sync_remains+trinket.2.cooldown.duration>fight_remains)))|trinket.1.cooldown.ready&variable.buff_sync_remains>20&variable.buff_sync_remains<trinket.1.cooldown.duration%3)))|!trinket.2.has_use_buff&(trinket.2.cast_time=0|!variable.buff_sync_active)&(!trinket.1.has_use_buff&(variable.trinket_2_stronger|trinket.1.cooldown.remains)|trinket.1.has_use_buff&(!variable.buff_sync_active&variable.buff_sync_remains>20|trinket.1.cooldown.remains>20))|boss&fight_remains<25&(variable.trinket_2_stronger|trinket.1.cooldown.remains)
